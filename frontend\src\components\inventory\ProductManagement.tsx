import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  InputNumber,
  Upload,
  Image,
  Tooltip,
  Badge,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  ShoppingOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface Product {
  id: string;
  sku: string;
  name: string;
  description: string;
  category: string;
  brand: string;
  unitPrice: number;
  costPrice: number;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  status: 'active' | 'inactive' | 'discontinued';
  supplier: string;
  location: string;
  lastUpdated: string;
  image?: string;
}

const ProductManagement: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([
    {
      id: '1',
      sku: 'PRD-001',
      name: 'Wireless Bluetooth Headphones',
      description: 'High-quality wireless headphones with noise cancellation',
      category: 'Electronics',
      brand: 'TechBrand',
      unitPrice: 199.99,
      costPrice: 120.00,
      currentStock: 45,
      minStock: 10,
      maxStock: 100,
      unit: 'piece',
      status: 'active',
      supplier: 'Electronics Supplier Inc.',
      location: 'Warehouse A - Section 1',
      lastUpdated: '2025-01-20',
    },
    {
      id: '2',
      sku: 'PRD-002',
      name: 'Office Chair - Ergonomic',
      description: 'Comfortable ergonomic office chair with lumbar support',
      category: 'Furniture',
      brand: 'ComfortSeating',
      unitPrice: 299.99,
      costPrice: 180.00,
      currentStock: 8,
      minStock: 15,
      maxStock: 50,
      unit: 'piece',
      status: 'active',
      supplier: 'Furniture Solutions Ltd.',
      location: 'Warehouse B - Section 2',
      lastUpdated: '2025-01-19',
    },
    {
      id: '3',
      sku: 'PRD-003',
      name: 'Laptop Stand - Adjustable',
      description: 'Adjustable aluminum laptop stand for better ergonomics',
      category: 'Accessories',
      brand: 'ErgoTech',
      unitPrice: 79.99,
      costPrice: 45.00,
      currentStock: 0,
      minStock: 20,
      maxStock: 80,
      unit: 'piece',
      status: 'active',
      supplier: 'Tech Accessories Co.',
      location: 'Warehouse A - Section 3',
      lastUpdated: '2025-01-18',
    },
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stockFilter, setStockFilter] = useState('all');
  const [form] = Form.useForm();

  const categories = ['Electronics', 'Furniture', 'Accessories', 'Office Supplies', 'Equipment'];
  const units = ['piece', 'kg', 'liter', 'meter', 'box', 'pack'];

  const getStatusColor = (status: string) => {
    const colors = {
      'active': 'green',
      'inactive': 'orange',
      'discontinued': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getStockStatus = (product: Product) => {
    if (product.currentStock === 0) return { status: 'out-of-stock', color: 'red', text: 'Out of Stock' };
    if (product.currentStock <= product.minStock) return { status: 'low-stock', color: 'orange', text: 'Low Stock' };
    if (product.currentStock >= product.maxStock) return { status: 'overstock', color: 'blue', text: 'Overstock' };
    return { status: 'in-stock', color: 'green', text: 'In Stock' };
  };

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchText.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || product.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter;
    
    let matchesStock = true;
    if (stockFilter === 'low-stock') {
      matchesStock = product.currentStock <= product.minStock && product.currentStock > 0;
    } else if (stockFilter === 'out-of-stock') {
      matchesStock = product.currentStock === 0;
    } else if (stockFilter === 'overstock') {
      matchesStock = product.currentStock >= product.maxStock;
    }
    
    return matchesSearch && matchesCategory && matchesStatus && matchesStock;
  });

  const handleCreateProduct = async (values: any) => {
    try {
      const newProduct: Product = {
        id: (products.length + 1).toString(),
        sku: values.sku,
        name: values.name,
        description: values.description,
        category: values.category,
        brand: values.brand,
        unitPrice: values.unitPrice,
        costPrice: values.costPrice,
        currentStock: values.currentStock || 0,
        minStock: values.minStock,
        maxStock: values.maxStock,
        unit: values.unit,
        status: 'active',
        supplier: values.supplier,
        location: values.location,
        lastUpdated: new Date().toISOString().split('T')[0],
      };

      setProducts([newProduct, ...products]);
      setShowCreateModal(false);
      form.resetFields();
      message.success('Product created successfully!');
    } catch (error) {
      message.error('Failed to create product');
    }
  };

  const handleUpdateStock = (productId: string, newStock: number) => {
    setProducts(prev => prev.map(product => 
      product.id === productId 
        ? { 
            ...product, 
            currentStock: newStock,
            lastUpdated: new Date().toISOString().split('T')[0],
          }
        : product
    ));
    message.success('Stock updated successfully!');
  };

  const columns = [
    {
      title: 'Product',
      key: 'product',
      width: 250,
      render: (_, record: Product) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div style={{ 
            width: 40, 
            height: 40, 
            backgroundColor: '#f0f0f0', 
            borderRadius: 4, 
            marginRight: 12,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <ShoppingOutlined style={{ color: '#666' }} />
          </div>
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.sku} • {record.brand}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      render: (category: string) => <Tag color="blue">{category}</Tag>,
    },
    {
      title: 'Stock Status',
      key: 'stockStatus',
      width: 150,
      render: (_, record: Product) => {
        const stockStatus = getStockStatus(record);
        const stockPercentage = record.maxStock > 0 ? (record.currentStock / record.maxStock) * 100 : 0;
        
        return (
          <div>
            <div style={{ marginBottom: 4 }}>
              <Tag color={stockStatus.color}>{stockStatus.text}</Tag>
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.currentStock} / {record.maxStock} {record.unit}
            </div>
            <Progress 
              percent={Math.min(stockPercentage, 100)}
              size="small"
              strokeColor={stockStatus.color}
              showInfo={false}
            />
          </div>
        );
      },
    },
    {
      title: 'Unit Price',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: 120,
      render: (price: number) => (
        <span style={{ fontWeight: 'bold' }}>
          ${price.toFixed(2)}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Cost Price',
      dataIndex: 'costPrice',
      key: 'costPrice',
      width: 120,
      render: (price: number) => (
        <span style={{ color: '#666' }}>
          ${price.toFixed(2)}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Margin',
      key: 'margin',
      width: 100,
      render: (_, record: Product) => {
        const margin = ((record.unitPrice - record.costPrice) / record.unitPrice) * 100;
        return (
          <span style={{ 
            color: margin > 30 ? '#52c41a' : margin > 15 ? '#fa8c16' : '#f5222d',
            fontWeight: 'bold'
          }}>
            {margin.toFixed(1)}%
          </span>
        );
      },
      align: 'center' as const,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 150,
      ellipsis: true,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: Product) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Edit Product">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => {
                setSelectedProduct(record);
                setShowEditModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Update Stock">
            <Button 
              type="text" 
              icon={<ShoppingOutlined />} 
              size="small"
              onClick={() => {
                const newStock = prompt(`Current stock: ${record.currentStock}. Enter new stock:`);
                if (newStock && !isNaN(Number(newStock))) {
                  handleUpdateStock(record.id, Number(newStock));
                }
              }}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button type="text" icon={<DeleteOutlined />} size="small" danger />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Calculate statistics
  const totalProducts = filteredProducts.length;
  const lowStockProducts = products.filter(p => p.currentStock <= p.minStock && p.currentStock > 0).length;
  const outOfStockProducts = products.filter(p => p.currentStock === 0).length;
  const totalValue = products.reduce((sum, p) => sum + (p.currentStock * p.costPrice), 0);

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Statistics */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Total Products</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                      {totalProducts}
                    </div>
                  </div>
                  <ShoppingOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Low Stock</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                      {lowStockProducts}
                    </div>
                  </div>
                  <WarningOutlined style={{ fontSize: '32px', color: '#fa8c16' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Out of Stock</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                      {outOfStockProducts}
                    </div>
                  </div>
                  <ExclamationCircleOutlined style={{ fontSize: '32px', color: '#f5222d' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Total Value</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                      ${totalValue.toLocaleString()}
                    </div>
                  </div>
                  <CheckCircleOutlined style={{ fontSize: '32px', color: '#52c41a' }} />
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Product Management */}
        <Col xs={24}>
          <Card
            title="Product Management"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowCreateModal(true)}
                >
                  Add Product
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={6}>
                <Input
                  placeholder="Search products..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Filter by category"
                  style={{ width: '100%' }}
                  value={categoryFilter}
                  onChange={setCategoryFilter}
                >
                  <Select.Option value="all">All Categories</Select.Option>
                  {categories.map(category => (
                    <Select.Option key={category} value={category}>{category}</Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Filter by status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="active">Active</Select.Option>
                  <Select.Option value="inactive">Inactive</Select.Option>
                  <Select.Option value="discontinued">Discontinued</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Filter by stock"
                  style={{ width: '100%' }}
                  value={stockFilter}
                  onChange={setStockFilter}
                >
                  <Select.Option value="all">All Stock Levels</Select.Option>
                  <Select.Option value="low-stock">Low Stock</Select.Option>
                  <Select.Option value="out-of-stock">Out of Stock</Select.Option>
                  <Select.Option value="overstock">Overstock</Select.Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredProducts}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} products`,
              }}
              scroll={{ x: 1400 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Create Product Modal */}
      <Modal
        title="Add New Product"
        open={showCreateModal}
        onCancel={() => {
          setShowCreateModal(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateProduct}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="sku"
                label="SKU"
                rules={[{ required: true, message: 'Please enter SKU' }]}
              >
                <Input placeholder="Enter product SKU" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="name"
                label="Product Name"
                rules={[{ required: true, message: 'Please enter product name' }]}
              >
                <Input placeholder="Enter product name" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="description"
                label="Description"
                rules={[{ required: true, message: 'Please enter description' }]}
              >
                <Input.TextArea rows={2} placeholder="Enter product description" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="category"
                label="Category"
                rules={[{ required: true, message: 'Please select category' }]}
              >
                <Select placeholder="Select category">
                  {categories.map(category => (
                    <Select.Option key={category} value={category}>{category}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="brand"
                label="Brand"
                rules={[{ required: true, message: 'Please enter brand' }]}
              >
                <Input placeholder="Enter brand name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="unit"
                label="Unit"
                rules={[{ required: true, message: 'Please select unit' }]}
              >
                <Select placeholder="Select unit">
                  {units.map(unit => (
                    <Select.Option key={unit} value={unit}>{unit}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="unitPrice"
                label="Unit Price"
                rules={[{ required: true, message: 'Please enter unit price' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  step={0.01}
                  placeholder="0.00"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="costPrice"
                label="Cost Price"
                rules={[{ required: true, message: 'Please enter cost price' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  step={0.01}
                  placeholder="0.00"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item name="currentStock" label="Current Stock">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="minStock"
                label="Minimum Stock"
                rules={[{ required: true, message: 'Please enter minimum stock' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="maxStock"
                label="Maximum Stock"
                rules={[{ required: true, message: 'Please enter maximum stock' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="supplier"
                label="Supplier"
                rules={[{ required: true, message: 'Please enter supplier' }]}
              >
                <Input placeholder="Enter supplier name" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="location"
                label="Storage Location"
                rules={[{ required: true, message: 'Please enter storage location' }]}
              >
                <Input placeholder="Enter storage location" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => setShowCreateModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Add Product</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagement;
