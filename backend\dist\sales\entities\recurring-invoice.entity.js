"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecurringInvoice = void 0;
const typeorm_1 = require("typeorm");
const customer_entity_1 = require("./customer.entity");
const recurring_invoice_item_entity_1 = require("./recurring-invoice-item.entity");
let RecurringInvoice = class RecurringInvoice {
    id;
    templateName;
    customerId;
    customer;
    frequency;
    startDate;
    endDate;
    nextInvoiceDate;
    lastInvoiceDate;
    invoicesGenerated;
    maxInvoices;
    subtotal;
    discountAmount;
    discountType;
    discountValue;
    taxAmount;
    totalAmount;
    status;
    paymentTerms;
    notes;
    terms;
    items;
    createdAt;
    updatedAt;
    tenantId;
};
exports.RecurringInvoice = RecurringInvoice;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "templateName", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "customerId", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => customer_entity_1.Customer, customer => customer),
    (0, typeorm_1.JoinColumn)({ name: 'customerId' }),
    __metadata("design:type", customer_entity_1.Customer)
], RecurringInvoice.prototype, "customer", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ['weekly', 'monthly', 'quarterly', 'yearly'], default: 'monthly' }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "frequency", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "startDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "endDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "nextInvoiceDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "lastInvoiceDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "invoicesGenerated", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', nullable: true }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "maxInvoices", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "subtotal", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "discountAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ['percentage', 'amount'], default: 'percentage' }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "discountType", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "discountValue", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "taxAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 10, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], RecurringInvoice.prototype, "totalAmount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'enum', enum: ['active', 'paused', 'completed', 'cancelled'], default: 'active' }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "paymentTerms", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "notes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "terms", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => recurring_invoice_item_entity_1.RecurringInvoiceItem, item => item.recurringInvoice, { cascade: true }),
    __metadata("design:type", Array)
], RecurringInvoice.prototype, "items", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "createdAt", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], RecurringInvoice.prototype, "updatedAt", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], RecurringInvoice.prototype, "tenantId", void 0);
exports.RecurringInvoice = RecurringInvoice = __decorate([
    (0, typeorm_1.Entity)('recurring_invoices')
], RecurringInvoice);
//# sourceMappingURL=recurring-invoice.entity.js.map