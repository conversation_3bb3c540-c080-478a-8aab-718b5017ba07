import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  DatePicker,
  InputNumber,
  Statistic,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  SwapOutlined,
  ShoppingOutlined,
  TruckOutlined,
  RetweetOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface StockMovement {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  movementType: 'in' | 'out' | 'transfer' | 'adjustment';
  quantity: number;
  unitPrice?: number;
  totalValue: number;
  reason: string;
  reference?: string;
  fromLocation?: string;
  toLocation?: string;
  date: string;
  performedBy: string;
  notes?: string;
  supplier?: string;
  customer?: string;
}

const StockMovements: React.FC = () => {
  const [movements, setMovements] = useState<StockMovement[]>([
    {
      id: '1',
      productId: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productSku: 'PRD-001',
      movementType: 'in',
      quantity: 50,
      unitPrice: 120.00,
      totalValue: 6000.00,
      reason: 'Purchase Order',
      reference: 'PO-2025-001',
      toLocation: 'Warehouse A - Section 1',
      date: '2025-01-20',
      performedBy: 'John Smith',
      supplier: 'Electronics Supplier Inc.',
    },
    {
      id: '2',
      productId: 'PRD-002',
      productName: 'Office Chair - Ergonomic',
      productSku: 'PRD-002',
      movementType: 'out',
      quantity: 5,
      unitPrice: 299.99,
      totalValue: 1499.95,
      reason: 'Sales Order',
      reference: 'SO-2025-015',
      fromLocation: 'Warehouse B - Section 2',
      date: '2025-01-19',
      performedBy: 'Sarah Johnson',
      customer: 'ABC Corporation',
    },
    {
      id: '3',
      productId: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productSku: 'PRD-001',
      movementType: 'transfer',
      quantity: 10,
      totalValue: 0,
      reason: 'Location Transfer',
      reference: 'TRF-2025-003',
      fromLocation: 'Warehouse A - Section 1',
      toLocation: 'Warehouse A - Section 2',
      date: '2025-01-18',
      performedBy: 'Mike Chen',
    },
    {
      id: '4',
      productId: 'PRD-003',
      productName: 'Laptop Stand - Adjustable',
      productSku: 'PRD-003',
      movementType: 'adjustment',
      quantity: -2,
      totalValue: -90.00,
      reason: 'Damaged Items',
      reference: 'ADJ-2025-001',
      fromLocation: 'Warehouse A - Section 3',
      date: '2025-01-17',
      performedBy: 'Lisa Wang',
      notes: 'Items damaged during handling',
    },
  ]);

  const [showMovementModal, setShowMovementModal] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [movementTypeFilter, setMovementTypeFilter] = useState('all');
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);
  const [form] = Form.useForm();

  const movementTypes = [
    { value: 'in', label: 'Stock In', color: 'green', icon: <ArrowUpOutlined /> },
    { value: 'out', label: 'Stock Out', color: 'red', icon: <ArrowDownOutlined /> },
    { value: 'transfer', label: 'Transfer', color: 'blue', icon: <SwapOutlined /> },
    { value: 'adjustment', label: 'Adjustment', color: 'orange', icon: <ExclamationCircleOutlined /> },
  ];

  const reasons = [
    'Purchase Order',
    'Sales Order',
    'Location Transfer',
    'Stock Adjustment',
    'Damaged Items',
    'Expired Items',
    'Return from Customer',
    'Return to Supplier',
    'Production Use',
    'Sample/Demo',
  ];

  const getMovementTypeConfig = (type: string) => {
    return movementTypes.find(mt => mt.value === type) || movementTypes[0];
  };

  const filteredMovements = movements.filter(movement => {
    const matchesSearch = movement.productName.toLowerCase().includes(searchText.toLowerCase()) ||
                         movement.productSku.toLowerCase().includes(searchText.toLowerCase()) ||
                         movement.reference?.toLowerCase().includes(searchText.toLowerCase() || '');
    const matchesType = movementTypeFilter === 'all' || movement.movementType === movementTypeFilter;
    
    let matchesDate = true;
    if (dateRange) {
      const movementDate = dayjs(movement.date);
      matchesDate = movementDate.isBetween(dateRange[0], dateRange[1], 'day', '[]');
    }
    
    return matchesSearch && matchesType && matchesDate;
  });

  const handleCreateMovement = async (values: any) => {
    try {
      const quantity = values.movementType === 'out' || values.movementType === 'adjustment' 
        ? -Math.abs(values.quantity) 
        : Math.abs(values.quantity);
      
      const totalValue = values.unitPrice ? quantity * values.unitPrice : 0;

      const newMovement: StockMovement = {
        id: (movements.length + 1).toString(),
        productId: values.productId,
        productName: values.productName || 'Selected Product',
        productSku: values.productSku || values.productId,
        movementType: values.movementType,
        quantity,
        unitPrice: values.unitPrice,
        totalValue,
        reason: values.reason,
        reference: values.reference,
        fromLocation: values.fromLocation,
        toLocation: values.toLocation,
        date: values.date.format('YYYY-MM-DD'),
        performedBy: 'Current User',
        notes: values.notes,
        supplier: values.supplier,
        customer: values.customer,
      };

      setMovements([newMovement, ...movements]);
      setShowMovementModal(false);
      form.resetFields();
      message.success('Stock movement recorded successfully!');
    } catch (error) {
      message.error('Failed to record stock movement');
    }
  };

  const columns = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
      sorter: (a: StockMovement, b: StockMovement) => dayjs(a.date).unix() - dayjs(b.date).unix(),
    },
    {
      title: 'Product',
      key: 'product',
      width: 200,
      render: (_, record: StockMovement) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.productName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.productSku}
          </div>
        </div>
      ),
    },
    {
      title: 'Movement Type',
      dataIndex: 'movementType',
      key: 'movementType',
      width: 130,
      render: (type: string) => {
        const config = getMovementTypeConfig(type);
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.label}
          </Tag>
        );
      },
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (quantity: number) => (
        <span style={{ 
          color: quantity > 0 ? '#52c41a' : '#f5222d',
          fontWeight: 'bold'
        }}>
          {quantity > 0 ? '+' : ''}{quantity}
        </span>
      ),
      align: 'center' as const,
    },
    {
      title: 'Value',
      dataIndex: 'totalValue',
      key: 'totalValue',
      width: 120,
      render: (value: number) => (
        <span style={{ 
          color: value > 0 ? '#52c41a' : value < 0 ? '#f5222d' : '#666',
          fontWeight: 'bold'
        }}>
          ${Math.abs(value).toFixed(2)}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Reason',
      dataIndex: 'reason',
      key: 'reason',
      width: 150,
    },
    {
      title: 'Reference',
      dataIndex: 'reference',
      key: 'reference',
      width: 120,
      render: (ref?: string) => ref || '-',
    },
    {
      title: 'Location',
      key: 'location',
      width: 200,
      render: (_, record: StockMovement) => {
        if (record.movementType === 'transfer') {
          return (
            <div style={{ fontSize: '12px' }}>
              <div>From: {record.fromLocation}</div>
              <div>To: {record.toLocation}</div>
            </div>
          );
        } else if (record.movementType === 'in') {
          return <Text style={{ fontSize: '12px' }}>To: {record.toLocation}</Text>;
        } else {
          return <Text style={{ fontSize: '12px' }}>From: {record.fromLocation}</Text>;
        }
      },
    },
    {
      title: 'Performed By',
      dataIndex: 'performedBy',
      key: 'performedBy',
      width: 120,
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 80,
      render: (_, record: StockMovement) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Calculate statistics
  const totalMovements = filteredMovements.length;
  const stockInMovements = filteredMovements.filter(m => m.movementType === 'in').length;
  const stockOutMovements = filteredMovements.filter(m => m.movementType === 'out').length;
  const totalValue = filteredMovements.reduce((sum, m) => sum + Math.abs(m.totalValue), 0);

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Statistics */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Movements"
                  value={totalMovements}
                  prefix={<SwapOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Stock In"
                  value={stockInMovements}
                  prefix={<ArrowUpOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Stock Out"
                  value={stockOutMovements}
                  prefix={<ArrowDownOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Value"
                  value={totalValue}
                  prefix="$"
                  formatter={(value) => value?.toLocaleString()}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Stock Movements */}
        <Col xs={24}>
          <Card
            title="Stock Movements"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowMovementModal(true)}
                >
                  Record Movement
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search products, SKU, reference..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by movement type"
                  style={{ width: '100%' }}
                  value={movementTypeFilter}
                  onChange={setMovementTypeFilter}
                >
                  <Select.Option value="all">All Movement Types</Select.Option>
                  {movementTypes.map(type => (
                    <Select.Option key={type.value} value={type.value}>
                      {type.label}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <RangePicker
                  style={{ width: '100%' }}
                  value={dateRange}
                  onChange={setDateRange}
                  placeholder={['Start Date', 'End Date']}
                />
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredMovements}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} movements`,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Record Movement Modal */}
      <Modal
        title="Record Stock Movement"
        open={showMovementModal}
        onCancel={() => {
          setShowMovementModal(false);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateMovement}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="productId"
                label="Product"
                rules={[{ required: true, message: 'Please select product' }]}
              >
                <Select placeholder="Select product">
                  <Select.Option value="PRD-001">Wireless Bluetooth Headphones (PRD-001)</Select.Option>
                  <Select.Option value="PRD-002">Office Chair - Ergonomic (PRD-002)</Select.Option>
                  <Select.Option value="PRD-003">Laptop Stand - Adjustable (PRD-003)</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="movementType"
                label="Movement Type"
                rules={[{ required: true, message: 'Please select movement type' }]}
              >
                <Select placeholder="Select movement type">
                  {movementTypes.map(type => (
                    <Select.Option key={type.value} value={type.value}>
                      {type.icon} {type.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="quantity"
                label="Quantity"
                rules={[{ required: true, message: 'Please enter quantity' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="Enter quantity"
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="date"
                label="Date"
                rules={[{ required: true, message: 'Please select date' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="unitPrice" label="Unit Price">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  step={0.01}
                  placeholder="0.00"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="reason"
                label="Reason"
                rules={[{ required: true, message: 'Please select reason' }]}
              >
                <Select placeholder="Select reason">
                  {reasons.map(reason => (
                    <Select.Option key={reason} value={reason}>{reason}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="reference" label="Reference">
                <Input placeholder="Enter reference number" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="fromLocation" label="From Location">
                <Input placeholder="Enter source location" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="toLocation" label="To Location">
                <Input placeholder="Enter destination location" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="notes" label="Notes">
                <Input.TextArea rows={2} placeholder="Enter additional notes" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => setShowMovementModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Record Movement</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default StockMovements;
