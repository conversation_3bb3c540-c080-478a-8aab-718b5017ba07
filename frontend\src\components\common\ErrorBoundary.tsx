import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Space } from 'antd';
import { 
  ExclamationCircleOutlined, 
  ReloadOutlined, 
  HomeOutlined,
  BugOutlined 
} from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by boundary:', error);
      console.error('Error info:', errorInfo);
    }

    // Call optional error handler
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // In production, you might want to send this to an error reporting service
    // Example: Sentry.captureException(error, { extra: errorInfo });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleGoHome = () => {
    window.location.href = '/';
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const isDevelopment = process.env.NODE_ENV === 'development';

      return (
        <div style={{ padding: '50px 20px', textAlign: 'center' }}>
          <Result
            status="error"
            icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
            title="Something went wrong"
            subTitle="We're sorry, but something unexpected happened. Please try again."
            extra={
              <Space direction="vertical" size="large">
                <Space>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />}
                    onClick={this.handleRetry}
                  >
                    Try Again
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />}
                    onClick={this.handleReload}
                  >
                    Reload Page
                  </Button>
                  <Button 
                    icon={<HomeOutlined />}
                    onClick={this.handleGoHome}
                  >
                    Go Home
                  </Button>
                </Space>

                {isDevelopment && this.state.error && (
                  <Card 
                    size="small" 
                    title={
                      <span>
                        <BugOutlined /> Development Error Details
                      </span>
                    }
                    style={{ 
                      textAlign: 'left', 
                      maxWidth: '800px', 
                      margin: '0 auto',
                      marginTop: '20px'
                    }}
                  >
                    <Paragraph>
                      <Text strong>Error:</Text>
                      <br />
                      <Text code style={{ fontSize: '12px' }}>
                        {this.state.error.message}
                      </Text>
                    </Paragraph>
                    
                    {this.state.error.stack && (
                      <Paragraph>
                        <Text strong>Stack Trace:</Text>
                        <br />
                        <pre style={{ 
                          fontSize: '10px', 
                          background: '#f5f5f5', 
                          padding: '10px',
                          overflow: 'auto',
                          maxHeight: '200px',
                          whiteSpace: 'pre-wrap'
                        }}>
                          {this.state.error.stack}
                        </pre>
                      </Paragraph>
                    )}

                    {this.state.errorInfo && (
                      <Paragraph>
                        <Text strong>Component Stack:</Text>
                        <br />
                        <pre style={{ 
                          fontSize: '10px', 
                          background: '#f5f5f5', 
                          padding: '10px',
                          overflow: 'auto',
                          maxHeight: '200px',
                          whiteSpace: 'pre-wrap'
                        }}>
                          {this.state.errorInfo.componentStack}
                        </pre>
                      </Paragraph>
                    )}
                  </Card>
                )}
              </Space>
            }
          />
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
