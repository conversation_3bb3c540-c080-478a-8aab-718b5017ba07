import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  Divider,
  Rate,
  Avatar,
  Tooltip,
  Badge,
  Statistic,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  GlobalOutlined,
  ShopOutlined,
  StarOutlined,
  DollarOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  website?: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  category: string;
  subCategory?: string;
  rating: number;
  status: 'active' | 'inactive' | 'pending' | 'suspended';
  paymentTerms: string;
  creditLimit: number;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  notes?: string;
  certifications: string[];
  taxId?: string;
  bankDetails?: {
    bankName: string;
    accountNumber: string;
    routingNumber: string;
  };
}

const SupplierManagement: React.FC = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([
    {
      id: '1',
      name: 'ABC Office Supplies Inc.',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '******-0123',
      website: 'www.abc-supplies.com',
      address: {
        street: '123 Business Ave',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA',
      },
      category: 'Office Supplies',
      subCategory: 'Furniture & Equipment',
      rating: 4.5,
      status: 'active',
      paymentTerms: 'Net 30',
      creditLimit: 50000,
      totalOrders: 25,
      totalSpent: 125000,
      lastOrderDate: '2025-01-15',
      notes: 'Reliable supplier with excellent customer service',
      certifications: ['ISO 9001', 'Green Business Certified'],
      taxId: '12-3456789',
    },
    {
      id: '2',
      name: 'Tech Solutions Ltd.',
      contactPerson: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0456',
      website: 'www.techsolutions.com',
      address: {
        street: '456 Tech Park',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94105',
        country: 'USA',
      },
      category: 'Technology',
      subCategory: 'Hardware & Software',
      rating: 4.8,
      status: 'active',
      paymentTerms: 'Net 15',
      creditLimit: 100000,
      totalOrders: 18,
      totalSpent: 85000,
      lastOrderDate: '2025-01-18',
      notes: 'Premium technology supplier with competitive pricing',
      certifications: ['ISO 27001', 'SOC 2 Type II'],
      taxId: '98-7654321',
    },
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [form] = Form.useForm();

  const categories = [
    'Office Supplies',
    'Technology',
    'Raw Materials',
    'Equipment',
    'Services',
    'Maintenance',
    'Utilities',
    'Professional Services',
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      'active': 'green',
      'inactive': 'gray',
      'pending': 'orange',
      'suspended': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const filteredSuppliers = suppliers.filter(supplier => {
    const matchesSearch = supplier.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         supplier.contactPerson.toLowerCase().includes(searchText.toLowerCase()) ||
                         supplier.email.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || supplier.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || supplier.status === statusFilter;
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const handleCreateSupplier = async (values: any) => {
    try {
      const newSupplier: Supplier = {
        id: (suppliers.length + 1).toString(),
        name: values.name,
        contactPerson: values.contactPerson,
        email: values.email,
        phone: values.phone,
        website: values.website,
        address: {
          street: values.street,
          city: values.city,
          state: values.state,
          zipCode: values.zipCode,
          country: values.country,
        },
        category: values.category,
        subCategory: values.subCategory,
        rating: 0,
        status: 'pending',
        paymentTerms: values.paymentTerms,
        creditLimit: values.creditLimit || 0,
        totalOrders: 0,
        totalSpent: 0,
        notes: values.notes,
        certifications: [],
        taxId: values.taxId,
      };

      setSuppliers([newSupplier, ...suppliers]);
      setShowCreateModal(false);
      form.resetFields();
      message.success('Supplier created successfully!');
    } catch (error) {
      message.error('Failed to create supplier');
    }
  };

  const handleStatusUpdate = (supplierId: string, newStatus: string) => {
    setSuppliers(prev => prev.map(supplier => 
      supplier.id === supplierId 
        ? { ...supplier, status: newStatus as any }
        : supplier
    ));
    message.success(`Supplier status updated to ${newStatus}!`);
  };

  const columns = [
    {
      title: 'Supplier',
      key: 'supplier',
      width: 250,
      render: (_, record: Supplier) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            icon={<ShopOutlined />} 
            style={{ marginRight: 12, backgroundColor: '#1890ff' }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.contactPerson}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Category',
      key: 'category',
      width: 150,
      render: (_, record: Supplier) => (
        <div>
          <Tag color="blue">{record.category}</Tag>
          {record.subCategory && (
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              {record.subCategory}
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Contact',
      key: 'contact',
      width: 150,
      render: (_, record: Supplier) => (
        <div>
          <div style={{ fontSize: '12px', marginBottom: 4 }}>
            <PhoneOutlined style={{ marginRight: 4 }} />
            {record.phone}
          </div>
          {record.website && (
            <div style={{ fontSize: '12px' }}>
              <GlobalOutlined style={{ marginRight: 4 }} />
              <a href={`https://${record.website}`} target="_blank" rel="noopener noreferrer">
                Website
              </a>
            </div>
          )}
        </div>
      ),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      width: 120,
      render: (rating: number) => (
        <div style={{ textAlign: 'center' }}>
          <Rate disabled value={rating} style={{ fontSize: '14px' }} />
          <div style={{ fontSize: '12px', color: '#666' }}>
            {rating.toFixed(1)}/5
          </div>
        </div>
      ),
    },
    {
      title: 'Orders',
      key: 'orders',
      width: 120,
      render: (_, record: Supplier) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontWeight: 'bold' }}>{record.totalOrders}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ${record.totalSpent.toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: 'Payment Terms',
      dataIndex: 'paymentTerms',
      key: 'paymentTerms',
      width: 120,
      render: (terms: string) => <Tag color="purple">{terms}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: Supplier) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                setSelectedSupplier(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button type="text" icon={<EditOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Contact">
            <Button 
              type="text" 
              icon={<MailOutlined />} 
              size="small"
              onClick={() => window.open(`mailto:${record.email}`)}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="Activate">
              <Button 
                type="text" 
                icon={<UserOutlined />} 
                size="small"
                style={{ color: '#52c41a' }}
                onClick={() => handleStatusUpdate(record.id, 'active')}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const totalSuppliers = filteredSuppliers.length;
  const activeSuppliers = filteredSuppliers.filter(s => s.status === 'active').length;
  const totalSpent = filteredSuppliers.reduce((sum, supplier) => sum + supplier.totalSpent, 0);
  const avgRating = filteredSuppliers.length > 0 
    ? filteredSuppliers.reduce((sum, supplier) => sum + supplier.rating, 0) / filteredSuppliers.length 
    : 0;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Summary Cards */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Suppliers"
                  value={totalSuppliers}
                  prefix={<ShopOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Active Suppliers"
                  value={activeSuppliers}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Spent"
                  value={totalSpent}
                  prefix={<DollarOutlined />}
                  formatter={(value) => `$${value?.toLocaleString()}`}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Avg Rating"
                  value={avgRating.toFixed(1)}
                  suffix="/5"
                  prefix={<StarOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Suppliers Table */}
        <Col xs={24}>
          <Card
            title="Supplier Management"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowCreateModal(true)}
                >
                  Add Supplier
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search suppliers..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by category"
                  style={{ width: '100%' }}
                  value={categoryFilter}
                  onChange={setCategoryFilter}
                >
                  <Select.Option value="all">All Categories</Select.Option>
                  {categories.map(category => (
                    <Select.Option key={category} value={category}>
                      {category}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="active">Active</Select.Option>
                  <Select.Option value="inactive">Inactive</Select.Option>
                  <Select.Option value="pending">Pending</Select.Option>
                  <Select.Option value="suspended">Suspended</Select.Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredSuppliers}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} suppliers`,
              }}
              scroll={{ x: 1400 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Create Supplier Modal */}
      <Modal
        title="Add New Supplier"
        open={showCreateModal}
        onCancel={() => {
          setShowCreateModal(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateSupplier}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="name"
                label="Supplier Name"
                rules={[{ required: true, message: 'Please enter supplier name' }]}
              >
                <Input placeholder="Enter supplier name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="contactPerson"
                label="Contact Person"
                rules={[{ required: true, message: 'Please enter contact person' }]}
              >
                <Input placeholder="Enter contact person name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="email"
                label="Email"
                rules={[
                  { required: true, message: 'Please enter email' },
                  { type: 'email', message: 'Please enter valid email' }
                ]}
              >
                <Input placeholder="Enter email address" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="phone"
                label="Phone"
                rules={[{ required: true, message: 'Please enter phone number' }]}
              >
                <Input placeholder="Enter phone number" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="website" label="Website">
                <Input placeholder="Enter website URL" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="taxId" label="Tax ID">
                <Input placeholder="Enter tax identification number" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="category"
                label="Category"
                rules={[{ required: true, message: 'Please select category' }]}
              >
                <Select placeholder="Select category">
                  {categories.map(category => (
                    <Select.Option key={category} value={category}>
                      {category}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="subCategory" label="Sub Category">
                <Input placeholder="Enter sub category" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="paymentTerms"
                label="Payment Terms"
                rules={[{ required: true, message: 'Please select payment terms' }]}
              >
                <Select placeholder="Select payment terms">
                  <Select.Option value="Net 15">Net 15</Select.Option>
                  <Select.Option value="Net 30">Net 30</Select.Option>
                  <Select.Option value="Net 60">Net 60</Select.Option>
                  <Select.Option value="COD">Cash on Delivery</Select.Option>
                  <Select.Option value="Prepaid">Prepaid</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="creditLimit" label="Credit Limit">
                <Input placeholder="Enter credit limit" type="number" />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">Address Information</Divider>

          <Row gutter={[16, 16]}>
            <Col xs={24}>
              <Form.Item
                name="street"
                label="Street Address"
                rules={[{ required: true, message: 'Please enter street address' }]}
              >
                <Input placeholder="Enter street address" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="city"
                label="City"
                rules={[{ required: true, message: 'Please enter city' }]}
              >
                <Input placeholder="Enter city" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="state"
                label="State/Province"
                rules={[{ required: true, message: 'Please enter state' }]}
              >
                <Input placeholder="Enter state/province" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={8}>
              <Form.Item
                name="zipCode"
                label="ZIP/Postal Code"
                rules={[{ required: true, message: 'Please enter ZIP code' }]}
              >
                <Input placeholder="Enter ZIP/postal code" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="country"
                label="Country"
                rules={[{ required: true, message: 'Please enter country' }]}
              >
                <Input placeholder="Enter country" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="notes" label="Notes">
            <Input.TextArea rows={3} placeholder="Enter any additional notes" />
          </Form.Item>

          <Divider />

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowCreateModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Add Supplier</Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* View Supplier Modal */}
      <Modal
        title={`Supplier Details - ${selectedSupplier?.name}`}
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setSelectedSupplier(null);
        }}
        footer={null}
        width={800}
      >
        {selectedSupplier && (
          <Row gutter={[24, 24]}>
            <Col xs={24} sm={12}>
              <Card size="small" title="Basic Information">
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Name: </Text>
                  <Text>{selectedSupplier.name}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Contact Person: </Text>
                  <Text>{selectedSupplier.contactPerson}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Email: </Text>
                  <Text>{selectedSupplier.email}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Phone: </Text>
                  <Text>{selectedSupplier.phone}</Text>
                </div>
                {selectedSupplier.website && (
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Website: </Text>
                    <a href={`https://${selectedSupplier.website}`} target="_blank" rel="noopener noreferrer">
                      {selectedSupplier.website}
                    </a>
                  </div>
                )}
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Category: </Text>
                  <Tag color="blue">{selectedSupplier.category}</Tag>
                </div>
                <div>
                  <Text strong>Status: </Text>
                  <Tag color={getStatusColor(selectedSupplier.status)}>
                    {selectedSupplier.status.toUpperCase()}
                  </Tag>
                </div>
              </Card>
            </Col>

            <Col xs={24} sm={12}>
              <Card size="small" title="Business Information">
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Payment Terms: </Text>
                  <Tag color="purple">{selectedSupplier.paymentTerms}</Tag>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Credit Limit: </Text>
                  <Text>${selectedSupplier.creditLimit.toLocaleString()}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Total Orders: </Text>
                  <Text>{selectedSupplier.totalOrders}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Total Spent: </Text>
                  <Text>${selectedSupplier.totalSpent.toLocaleString()}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>Rating: </Text>
                  <Rate disabled value={selectedSupplier.rating} style={{ fontSize: '14px' }} />
                  <Text style={{ marginLeft: 8 }}>({selectedSupplier.rating}/5)</Text>
                </div>
                {selectedSupplier.taxId && (
                  <div>
                    <Text strong>Tax ID: </Text>
                    <Text>{selectedSupplier.taxId}</Text>
                  </div>
                )}
              </Card>
            </Col>

            <Col xs={24}>
              <Card size="small" title="Address">
                <Text>
                  {selectedSupplier.address.street}<br />
                  {selectedSupplier.address.city}, {selectedSupplier.address.state} {selectedSupplier.address.zipCode}<br />
                  {selectedSupplier.address.country}
                </Text>
              </Card>
            </Col>

            {selectedSupplier.certifications.length > 0 && (
              <Col xs={24}>
                <Card size="small" title="Certifications">
                  <Space wrap>
                    {selectedSupplier.certifications.map((cert, index) => (
                      <Tag key={index} color="green">{cert}</Tag>
                    ))}
                  </Space>
                </Card>
              </Col>
            )}

            {selectedSupplier.notes && (
              <Col xs={24}>
                <Card size="small" title="Notes">
                  <Text>{selectedSupplier.notes}</Text>
                </Card>
              </Col>
            )}
          </Row>
        )}
      </Modal>
    </div>
  );
};

export default SupplierManagement;
