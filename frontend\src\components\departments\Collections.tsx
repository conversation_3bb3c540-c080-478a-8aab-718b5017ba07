import React, { useState } from 'react';
import { Tabs, Typography, Table, Card, Row, Col, Statistic, Tag, Button, Space } from 'antd';
import {
  DashboardOutlined,
  DollarOutlined,
  PhoneOutlined,
  MailOutlined,
  FileTextOutlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import styled from 'styled-components';
import OverdueAccountsManagement from '../collections/OverdueAccountsManagement';

const { Title } = Typography;

const CollectionsContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }
`;

interface OverdueAccount {
  id: string;
  customerName: string;
  invoiceNumber: string;
  amount: number;
  daysOverdue: number;
  lastContact: string;
  status: 'New' | 'In Progress' | 'Resolved' | 'Written Off';
}

const Collections: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');

  const overdueAccounts: OverdueAccount[] = [
    {
      id: '1',
      customerName: 'ABC Corporation',
      invoiceNumber: 'INV-001',
      amount: 15000,
      daysOverdue: 45,
      lastContact: '2024-05-15',
      status: 'In Progress',
    },
    {
      id: '2',
      customerName: 'XYZ Ltd',
      invoiceNumber: 'INV-002',
      amount: 8500,
      daysOverdue: 30,
      lastContact: '2024-05-18',
      status: 'New',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'New': return 'red';
      case 'In Progress': return 'orange';
      case 'Resolved': return 'green';
      case 'Written Off': return 'gray';
      default: return 'default';
    }
  };

  const columns: ColumnsType<OverdueAccount> = [
    {
      title: 'Customer',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: 'Invoice',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `$${amount.toLocaleString()}`,
    },
    {
      title: 'Days Overdue',
      dataIndex: 'daysOverdue',
      key: 'daysOverdue',
      render: (days) => (
        <span style={{ color: days > 30 ? '#f5222d' : '#fa8c16' }}>
          {days} days
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<PhoneOutlined />} size="small" />
          <Button type="text" icon={<MailOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  const CollectionsDashboard = () => (
    <div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Overdue"
              value={125000}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#f5222d' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Overdue Accounts"
              value={23}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Collection Rate"
              value={85.5}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Avg Days to Collect"
              value={28}
              suffix=" days"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="Overdue Accounts">
        <Table
          columns={columns}
          dataSource={overdueAccounts}
          rowKey="id"
          pagination={false}
        />
      </Card>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          {t('collections.dashboard')}
        </span>
      ),
      children: <CollectionsDashboard />,
    },
    {
      key: 'overdue',
      label: (
        <span>
          <DollarOutlined />
          {t('collections.overdueAccounts')}
        </span>
      ),
      children: <OverdueAccountsManagement />,
    },
    {
      key: 'reminders',
      label: (
        <span>
          <MailOutlined />
          Payment Reminders
        </span>
      ),
      children: (
        <Card title="Payment Reminder System">
          <p>Automated payment reminder system will be implemented here.</p>
        </Card>
      ),
    },
    {
      key: 'calls',
      label: (
        <span>
          <PhoneOutlined />
          Collection Calls
        </span>
      ),
      children: (
        <Card title="Collection Call Management">
          <p>Collection call scheduling and tracking system.</p>
        </Card>
      ),
    },
    {
      key: 'reports',
      label: (
        <span>
          <FileTextOutlined />
          Reports
        </span>
      ),
      children: (
        <Card title="Collection Reports">
          <p>Collection performance reports and analytics.</p>
        </Card>
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Settings
        </span>
      ),
      children: (
        <Card title="Collection Settings">
          <p>Configure collection rules and automation settings.</p>
        </Card>
      ),
    },
  ];

  return (
    <CollectionsContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('collections.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />
    </CollectionsContainer>
  );
};

export default Collections;
