"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditReport = void 0;
const typeorm_1 = require("typeorm");
const company_entity_1 = require("../../company/entities/company.entity");
const user_entity_1 = require("../../user/entities/user.entity");
const audit_finding_entity_1 = require("./audit-finding.entity");
let AuditReport = class AuditReport {
    id;
    companyId;
    year;
    quarter;
    reportType;
    status;
    department;
    scope;
    auditCategory;
    regulatoryFramework;
    riskLevel;
    auditor;
    auditScope;
    objectives;
    methodology;
    executiveSummary;
    complianceScore;
    totalFindings;
    criticalFindings;
    highFindings;
    mediumFindings;
    lowFindings;
    recommendations;
    plannedStartDate;
    plannedEndDate;
    actualStartDate;
    actualEndDate;
    completedDate;
    auditTeam;
    auditCriteria;
    riskAssessment;
    samplingMethod;
    limitations;
    conclusion;
    managementResponse;
    attachments;
    distributionList;
    nextAuditDate;
    followUpActions;
    financialData;
    specializedData;
    createdBy;
    updatedBy;
    createdDate;
    updatedDate;
    company;
    creator;
    updater;
    findings;
};
exports.AuditReport = AuditReport;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AuditReport.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'company_id' }),
    __metadata("design:type", String)
], AuditReport.prototype, "companyId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", Number)
], AuditReport.prototype, "year", void 0);
__decorate([
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", Number)
], AuditReport.prototype, "quarter", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: [
            'Annual', 'Quarterly', 'Special', 'Financial', 'Comprehensive',
            'BSA_AML', 'Operations_Compliance', 'Branch_Operations', 'ACH_Cash_Management',
            'ALM', 'IT_Security', 'Network_Penetration', 'Credit_Review', 'SBA_Lending',
            'Trust_Operations', 'SOX_FDICIA', 'Enterprise_Risk'
        ],
        default: 'Annual',
    }),
    __metadata("design:type", String)
], AuditReport.prototype, "reportType", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['Draft', 'In Review', 'Approved', 'Published'],
        default: 'Draft',
    }),
    __metadata("design:type", String)
], AuditReport.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditReport.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['Single Department', 'All Departments', 'Financial Only', 'Comprehensive', 'Institution_Wide', 'Regulatory_Focus'],
        default: 'Single Department',
    }),
    __metadata("design:type", String)
], AuditReport.prototype, "scope", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['General', 'Financial_Institution', 'Regulatory_Compliance', 'Risk_Management', 'Technology', 'Operations'],
        default: 'General',
    }),
    __metadata("design:type", String)
], AuditReport.prototype, "auditCategory", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'simple-array', nullable: true }),
    __metadata("design:type", Array)
], AuditReport.prototype, "regulatoryFramework", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['Low', 'Medium', 'High', 'Critical'],
        default: 'Medium',
    }),
    __metadata("design:type", String)
], AuditReport.prototype, "riskLevel", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditReport.prototype, "auditor", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "auditScope", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "objectives", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "methodology", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "executiveSummary", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 5, scale: 2, default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "complianceScore", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "totalFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "criticalFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "highFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "mediumFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "lowFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditReport.prototype, "recommendations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "plannedStartDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "plannedEndDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "actualStartDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "actualEndDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "completedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "auditTeam", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "auditCriteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "riskAssessment", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "samplingMethod", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "limitations", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "conclusion", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "managementResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditReport.prototype, "attachments", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditReport.prototype, "distributionList", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditReport.prototype, "nextAuditDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditReport.prototype, "followUpActions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "financialData", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditReport.prototype, "specializedData", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by' }),
    __metadata("design:type", String)
], AuditReport.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true }),
    __metadata("design:type", String)
], AuditReport.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_date' }),
    __metadata("design:type", Date)
], AuditReport.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_date' }),
    __metadata("design:type", Date)
], AuditReport.prototype, "updatedDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => company_entity_1.Company),
    (0, typeorm_1.JoinColumn)({ name: 'company_id' }),
    __metadata("design:type", company_entity_1.Company)
], AuditReport.prototype, "company", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], AuditReport.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], AuditReport.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.OneToMany)(() => audit_finding_entity_1.AuditFinding, (finding) => finding.auditReport),
    __metadata("design:type", Array)
], AuditReport.prototype, "findings", void 0);
exports.AuditReport = AuditReport = __decorate([
    (0, typeorm_1.Entity)('audit_reports')
], AuditReport);
//# sourceMappingURL=audit-report.entity.js.map