import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  DatePicker,
  TimePicker,
  Statistic,
  Calendar,
  Badge,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CalendarOutlined,
  UserOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface AttendanceRecord {
  id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  date: string;
  checkIn: string;
  checkOut?: string;
  workingHours: number;
  status: 'present' | 'late' | 'absent' | 'half-day' | 'overtime';
  notes?: string;
  location: 'office' | 'remote' | 'field';
}

interface Employee {
  id: string;
  name: string;
  department: string;
  position: string;
  workSchedule: {
    startTime: string;
    endTime: string;
    workingDays: string[];
  };
}

const AttendanceManagement: React.FC = () => {
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([
    {
      id: '1',
      employeeId: 'EMP001',
      employeeName: 'John Smith',
      department: 'IT',
      date: '2025-01-20',
      checkIn: '09:00',
      checkOut: '17:30',
      workingHours: 8.5,
      status: 'present',
      location: 'office',
    },
    {
      id: '2',
      employeeId: 'EMP002',
      employeeName: 'Sarah Johnson',
      department: 'Finance',
      date: '2025-01-20',
      checkIn: '09:15',
      checkOut: '17:45',
      workingHours: 8.5,
      status: 'late',
      location: 'office',
      notes: 'Traffic delay',
    },
    {
      id: '3',
      employeeId: 'EMP003',
      employeeName: 'Mike Chen',
      department: 'Sales',
      date: '2025-01-20',
      checkIn: '08:45',
      checkOut: '18:30',
      workingHours: 9.75,
      status: 'overtime',
      location: 'remote',
    },
  ]);

  const [employees] = useState<Employee[]>([
    {
      id: 'EMP001',
      name: 'John Smith',
      department: 'IT',
      position: 'Software Developer',
      workSchedule: {
        startTime: '09:00',
        endTime: '17:00',
        workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      },
    },
    {
      id: 'EMP002',
      name: 'Sarah Johnson',
      department: 'Finance',
      position: 'Financial Analyst',
      workSchedule: {
        startTime: '09:00',
        endTime: '17:00',
        workingDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      },
    },
  ]);

  const [showAttendanceModal, setShowAttendanceModal] = useState(false);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [searchText, setSearchText] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [form] = Form.useForm();

  const departments = ['IT', 'Finance', 'Sales', 'HR', 'Marketing', 'Operations'];

  const getStatusColor = (status: string) => {
    const colors = {
      'present': 'green',
      'late': 'orange',
      'absent': 'red',
      'half-day': 'blue',
      'overtime': 'purple',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getLocationColor = (location: string) => {
    const colors = {
      'office': 'blue',
      'remote': 'green',
      'field': 'orange',
    };
    return colors[location as keyof typeof colors] || 'default';
  };

  const filteredRecords = attendanceRecords.filter(record => {
    const matchesSearch = record.employeeName.toLowerCase().includes(searchText.toLowerCase()) ||
                         record.employeeId.toLowerCase().includes(searchText.toLowerCase());
    const matchesDepartment = departmentFilter === 'all' || record.department === departmentFilter;
    const matchesStatus = statusFilter === 'all' || record.status === statusFilter;
    const matchesDate = record.date === selectedDate.format('YYYY-MM-DD');
    return matchesSearch && matchesDepartment && matchesStatus && matchesDate;
  });

  const handleMarkAttendance = async (values: any) => {
    try {
      const checkInTime = values.checkIn.format('HH:mm');
      const checkOutTime = values.checkOut?.format('HH:mm');
      
      let workingHours = 0;
      let status: AttendanceRecord['status'] = 'present';
      
      if (checkOutTime) {
        const checkIn = dayjs(`2025-01-01 ${checkInTime}`);
        const checkOut = dayjs(`2025-01-01 ${checkOutTime}`);
        workingHours = checkOut.diff(checkIn, 'hour', true);
        
        // Determine status based on timing
        if (checkInTime > '09:15') status = 'late';
        else if (workingHours > 9) status = 'overtime';
        else if (workingHours < 4) status = 'half-day';
      }

      const newRecord: AttendanceRecord = {
        id: (attendanceRecords.length + 1).toString(),
        employeeId: values.employeeId,
        employeeName: employees.find(emp => emp.id === values.employeeId)?.name || '',
        department: employees.find(emp => emp.id === values.employeeId)?.department || '',
        date: values.date.format('YYYY-MM-DD'),
        checkIn: checkInTime,
        checkOut: checkOutTime,
        workingHours,
        status,
        location: values.location,
        notes: values.notes,
      };

      setAttendanceRecords([newRecord, ...attendanceRecords]);
      setShowAttendanceModal(false);
      form.resetFields();
      message.success('Attendance marked successfully!');
    } catch (error) {
      message.error('Failed to mark attendance');
    }
  };

  const columns = [
    {
      title: 'Employee',
      key: 'employee',
      width: 200,
      render: (_, record: AttendanceRecord) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.employeeName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.employeeId} • {record.department}
          </div>
        </div>
      ),
    },
    {
      title: 'Check In',
      dataIndex: 'checkIn',
      key: 'checkIn',
      width: 100,
      render: (time: string) => (
        <span style={{ fontWeight: 500 }}>{time}</span>
      ),
    },
    {
      title: 'Check Out',
      dataIndex: 'checkOut',
      key: 'checkOut',
      width: 100,
      render: (time?: string) => (
        <span style={{ fontWeight: 500 }}>
          {time || <Text type="secondary">Not checked out</Text>}
        </span>
      ),
    },
    {
      title: 'Working Hours',
      dataIndex: 'workingHours',
      key: 'workingHours',
      width: 120,
      render: (hours: number) => (
        <span style={{ fontWeight: 500 }}>
          {hours > 0 ? `${hours.toFixed(1)}h` : '-'}
        </span>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      width: 100,
      render: (location: string) => (
        <Tag color={getLocationColor(location)}>
          {location.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
      render: (notes?: string) => notes || '-',
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: AttendanceRecord) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Edit">
            <Button type="text" icon={<EditOutlined />} size="small" />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // Calculate daily statistics
  const todayRecords = attendanceRecords.filter(r => r.date === selectedDate.format('YYYY-MM-DD'));
  const presentCount = todayRecords.filter(r => r.status === 'present' || r.status === 'late' || r.status === 'overtime').length;
  const lateCount = todayRecords.filter(r => r.status === 'late').length;
  const absentCount = todayRecords.filter(r => r.status === 'absent').length;
  const avgWorkingHours = todayRecords.length > 0 
    ? todayRecords.reduce((sum, r) => sum + r.workingHours, 0) / todayRecords.length 
    : 0;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Daily Statistics */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Present Today"
                  value={presentCount}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Late Arrivals"
                  value={lateCount}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Absent"
                  value={absentCount}
                  prefix={<ExclamationCircleOutlined />}
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Avg Working Hours"
                  value={avgWorkingHours.toFixed(1)}
                  suffix="h"
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Attendance Management */}
        <Col xs={24}>
          <Card
            title={`Attendance Records - ${selectedDate.format('MMMM DD, YYYY')}`}
            extra={
              <Space>
                <DatePicker
                  value={selectedDate}
                  onChange={(date) => setSelectedDate(date || dayjs())}
                  format="YYYY-MM-DD"
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowAttendanceModal(true)}
                >
                  Mark Attendance
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search employees..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by department"
                  style={{ width: '100%' }}
                  value={departmentFilter}
                  onChange={setDepartmentFilter}
                >
                  <Select.Option value="all">All Departments</Select.Option>
                  {departments.map(dept => (
                    <Select.Option key={dept} value={dept}>{dept}</Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="present">Present</Select.Option>
                  <Select.Option value="late">Late</Select.Option>
                  <Select.Option value="absent">Absent</Select.Option>
                  <Select.Option value="half-day">Half Day</Select.Option>
                  <Select.Option value="overtime">Overtime</Select.Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredRecords}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} records`,
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Mark Attendance Modal */}
      <Modal
        title="Mark Attendance"
        open={showAttendanceModal}
        onCancel={() => {
          setShowAttendanceModal(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleMarkAttendance}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="employeeId"
                label="Employee"
                rules={[{ required: true, message: 'Please select employee' }]}
              >
                <Select placeholder="Select employee">
                  {employees.map(emp => (
                    <Select.Option key={emp.id} value={emp.id}>
                      {emp.name} ({emp.id})
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="date"
                label="Date"
                rules={[{ required: true, message: 'Please select date' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="checkIn"
                label="Check In Time"
                rules={[{ required: true, message: 'Please select check in time' }]}
              >
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="checkOut" label="Check Out Time">
                <TimePicker style={{ width: '100%' }} format="HH:mm" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="location"
                label="Work Location"
                rules={[{ required: true, message: 'Please select location' }]}
              >
                <Select placeholder="Select location">
                  <Select.Option value="office">Office</Select.Option>
                  <Select.Option value="remote">Remote</Select.Option>
                  <Select.Option value="field">Field Work</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="notes" label="Notes">
                <Input.TextArea rows={2} placeholder="Enter any notes or comments" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => setShowAttendanceModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Mark Attendance</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AttendanceManagement;
