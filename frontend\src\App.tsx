import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider } from 'antd';
import { useTranslation } from 'react-i18next';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/common/ProtectedRoute';
import DashboardLayout from './components/layout/DashboardLayout';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import Dashboard from './components/dashboard/Dashboard';
import Sales from './components/departments/Sales';
import CRM from './components/departments/CRM';
import GeneralManager from './components/departments/GeneralManager';
import PointOfSale from './components/departments/PointOfSale';
import Customers from './components/departments/Customers';
import Projects from './components/departments/Projects';
import Finance from './components/departments/Finance';
import Collections from './components/departments/Collections';
import Procurement from './components/departments/Procurement';
import Inventory from './components/departments/Inventory';
import HumanResources from './components/departments/HumanResources';
import ITSupport from './components/departments/ITSupport';
import Legal from './components/departments/Legal';
import Analytics from './components/departments/Analytics';
import SystemGuide from './components/departments/SystemGuide';
import Settings from './components/departments/Settings';
import './i18n';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Set document direction based on language
    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider
        direction={i18n.language === 'ar' ? 'rtl' : 'ltr'}
        theme={{
          token: {
            colorPrimary: '#667eea',
            borderRadius: 8,
          },
        }}
      >
        <AuthProvider>
          <Router>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <DashboardLayout />
                  </ProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/dashboard" replace />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="general-manager" element={<GeneralManager />} />
                <Route path="sales" element={<Sales />} />
                <Route path="crm" element={<CRM />} />
                <Route path="pos" element={<PointOfSale />} />
                <Route path="customers" element={<Customers />} />
                <Route path="projects" element={<Projects />} />
                <Route path="finance" element={<Finance />} />
                <Route path="collections" element={<Collections />} />
                <Route path="procurement" element={<Procurement />} />
                <Route path="inventory" element={<Inventory />} />
                <Route path="hr" element={<HumanResources />} />
                <Route path="it-support" element={<ITSupport />} />
                <Route path="legal" element={<Legal />} />
                <Route path="analytics" element={<Analytics />} />
                <Route path="system-guide" element={<SystemGuide />} />
                <Route path="settings" element={<Settings />} />
                <Route path="profile" element={<div>Profile Page</div>} />
              </Route>
              <Route path="*" element={<Navigate to="/dashboard" replace />} />
            </Routes>
          </Router>
        </AuthProvider>
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
