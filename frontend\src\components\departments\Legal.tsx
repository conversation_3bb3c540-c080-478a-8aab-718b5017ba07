import React, { useState } from 'react';
import { Tabs, Typography, Card, Row, Col, Statistic, Table, Tag, Button, Space, Progress, Avatar, List } from 'antd';
import {
  DashboardOutlined,
  FileTextOutlined,
  SafetyOutlined,
  AuditOutlined,
  TeamOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  AlertOutlined,
  BankOutlined,
  UserOutlined,
  WarningOutlined,
  BookOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;

const LegalContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }
`;

interface LegalCase {
  id: string;
  caseNumber: string;
  title: string;
  type: 'Contract Dispute' | 'Employment' | 'Intellectual Property' | 'Compliance' | 'Litigation';
  status: 'Active' | 'Pending' | 'Closed' | 'On Hold';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  assignedLawyer: string;
  client: string;
  startDate: string;
  dueDate: string;
  estimatedValue: number;
}

interface Contract {
  id: string;
  contractNumber: string;
  title: string;
  type: 'Service Agreement' | 'Employment' | 'NDA' | 'Partnership' | 'Vendor';
  status: 'Draft' | 'Under Review' | 'Approved' | 'Executed' | 'Expired';
  party: string;
  startDate: string;
  endDate: string;
  value: number;
  renewalDate?: string;
}

interface ComplianceItem {
  id: string;
  regulation: string;
  description: string;
  status: 'Compliant' | 'Non-Compliant' | 'Under Review' | 'Action Required';
  lastReview: string;
  nextReview: string;
  responsible: string;
  riskLevel: 'Low' | 'Medium' | 'High' | 'Critical';
}

const Legal: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');

  // Sample data for legal cases
  const legalCases: LegalCase[] = [
    {
      id: '1',
      caseNumber: 'LC-2025-001',
      title: 'Contract Breach - ABC Corp',
      type: 'Contract Dispute',
      status: 'Active',
      priority: 'High',
      assignedLawyer: 'Sarah Johnson',
      client: 'ABC Corporation',
      startDate: '2025-01-10',
      dueDate: '2025-03-15',
      estimatedValue: 250000,
    },
    {
      id: '2',
      caseNumber: 'LC-2025-002',
      title: 'Employment Termination Review',
      type: 'Employment',
      status: 'Pending',
      priority: 'Medium',
      assignedLawyer: 'Michael Chen',
      client: 'Internal HR',
      startDate: '2025-01-18',
      dueDate: '2025-02-28',
      estimatedValue: 75000,
    },
  ];

  // Sample data for contracts
  const contracts: Contract[] = [
    {
      id: '1',
      contractNumber: 'CNT-2025-001',
      title: 'Software License Agreement',
      type: 'Service Agreement',
      status: 'Executed',
      party: 'TechSoft Solutions',
      startDate: '2025-01-01',
      endDate: '2025-12-31',
      value: 120000,
      renewalDate: '2025-11-01',
    },
    {
      id: '2',
      contractNumber: 'CNT-2025-002',
      title: 'Office Lease Agreement',
      type: 'Service Agreement',
      status: 'Under Review',
      party: 'Property Management LLC',
      startDate: '2025-02-01',
      endDate: '2027-01-31',
      value: 480000,
    },
  ];

  // Sample data for compliance
  const complianceItems: ComplianceItem[] = [
    {
      id: '1',
      regulation: 'GDPR Compliance',
      description: 'General Data Protection Regulation compliance review',
      status: 'Compliant',
      lastReview: '2024-12-15',
      nextReview: '2025-06-15',
      responsible: 'Data Protection Officer',
      riskLevel: 'High',
    },
    {
      id: '2',
      regulation: 'SOX Compliance',
      description: 'Sarbanes-Oxley Act financial reporting compliance',
      status: 'Under Review',
      lastReview: '2024-11-30',
      nextReview: '2025-02-28',
      responsible: 'Finance Team',
      riskLevel: 'Critical',
    },
  ];

  const getStatusColor = (status: string) => {
    const colors: { [key: string]: string } = {
      'Active': 'green',
      'Pending': 'orange',
      'Closed': 'gray',
      'On Hold': 'red',
      'Draft': 'default',
      'Under Review': 'blue',
      'Approved': 'cyan',
      'Executed': 'green',
      'Expired': 'red',
      'Compliant': 'green',
      'Non-Compliant': 'red',
      'Action Required': 'orange',
    };
    return colors[status] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors: { [key: string]: string } = {
      'Low': 'green',
      'Medium': 'orange',
      'High': 'red',
      'Critical': 'magenta',
    };
    return colors[priority] || 'default';
  };

  const getRiskColor = (risk: string) => {
    const colors: { [key: string]: string } = {
      'Low': 'green',
      'Medium': 'orange',
      'High': 'red',
      'Critical': 'magenta',
    };
    return colors[risk] || 'default';
  };

  // Calculate dashboard metrics
  const activeCases = legalCases.filter(c => c.status === 'Active').length;
  const pendingCases = legalCases.filter(c => c.status === 'Pending').length;
  const totalCaseValue = legalCases.reduce((sum, c) => sum + c.estimatedValue, 0);
  const activeContracts = contracts.filter(c => c.status === 'Executed').length;
  const contractsUnderReview = contracts.filter(c => c.status === 'Under Review').length;
  const totalContractValue = contracts.reduce((sum, c) => sum + c.value, 0);
  const complianceIssues = complianceItems.filter(c => c.status === 'Non-Compliant' || c.status === 'Action Required').length;
  const compliantItems = complianceItems.filter(c => c.status === 'Compliant').length;

  const caseColumns: ColumnsType<LegalCase> = [
    {
      title: 'Case',
      key: 'case',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.caseNumber}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color="blue">{type}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => <Tag color={getPriorityColor(priority)}>{priority}</Tag>,
    },
    {
      title: 'Assigned Lawyer',
      dataIndex: 'assignedLawyer',
      key: 'assignedLawyer',
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
    },
  ];

  const contractColumns: ColumnsType<Contract> = [
    {
      title: 'Contract',
      key: 'contract',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.contractNumber}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>
        </div>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color="purple">{type}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: 'Party',
      dataIndex: 'party',
      key: 'party',
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (value) => `$${value.toLocaleString()}`,
    },
    {
      title: 'End Date',
      dataIndex: 'endDate',
      key: 'endDate',
    },
  ];

  const LegalDashboard = () => (
    <div>
      {/* Key Metrics */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Cases"
              value={activeCases}
              prefix={<AuditOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Pending Cases"
              value={pendingCases}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Contracts"
              value={activeContracts}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Compliance Issues"
              value={complianceIssues}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: complianceIssues > 0 ? '#f5222d' : '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Financial Overview */}
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Case Value"
              value={totalCaseValue}
              prefix="$"
              formatter={(value) => value?.toLocaleString()}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Contract Value"
              value={totalContractValue}
              prefix="$"
              formatter={(value) => value?.toLocaleString()}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Compliance Rate"
              value={Math.round((compliantItems / complianceItems.length) * 100)}
              suffix="%"
              valueStyle={{ color: compliantItems / complianceItems.length > 0.8 ? '#52c41a' : '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Recent Activity */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Recent Legal Cases" extra={<Button type="link">View All</Button>}>
            <Table
              columns={caseColumns}
              dataSource={legalCases.slice(0, 5)}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Contract Status Overview" extra={<Button type="link">View All</Button>}>
            <Table
              columns={contractColumns}
              dataSource={contracts.slice(0, 5)}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Compliance Status */}
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24}>
          <Card title="Compliance Status Overview">
            <Row gutter={[16, 16]}>
              {complianceItems.map((item) => (
                <Col xs={24} sm={12} lg={8} key={item.id}>
                  <Card size="small" style={{ height: '100%' }}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>{item.regulation}</Text>
                      <Tag
                        color={getStatusColor(item.status)}
                        style={{ float: 'right' }}
                      >
                        {item.status}
                      </Tag>
                    </div>
                    <div style={{ marginBottom: 8, fontSize: '12px', color: '#666' }}>
                      {item.description}
                    </div>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>Risk Level: </Text>
                      <Tag color={getRiskColor(item.riskLevel)} size="small">
                        {item.riskLevel}
                      </Tag>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div>Last Review: {item.lastReview}</div>
                      <div>Next Review: {item.nextReview}</div>
                      <div>Responsible: {item.responsible}</div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        <Col xs={24}>
          <Card title="Quick Actions">
            <Space wrap>
              <Button type="primary" icon={<FileTextOutlined />}>
                New Contract
              </Button>
              <Button icon={<AuditOutlined />}>
                New Legal Case
              </Button>
              <Button icon={<SafetyOutlined />}>
                Compliance Review
              </Button>
              <Button icon={<BookOutlined />}>
                Legal Research
              </Button>
              <Button icon={<TeamOutlined />}>
                Schedule Meeting
              </Button>
              <Button icon={<AlertOutlined />}>
                Risk Assessment
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          {t('legal.dashboard')}
        </span>
      ),
      children: <LegalDashboard />,
    },
    {
      key: 'cases',
      label: (
        <span>
          <AuditOutlined />
          {t('legal.cases')}
        </span>
      ),
      children: (
        <Card title="Legal Cases Management">
          <p>Comprehensive legal case management system.</p>
        </Card>
      ),
    },
    {
      key: 'contracts',
      label: (
        <span>
          <FileTextOutlined />
          {t('legal.contracts')}
        </span>
      ),
      children: (
        <Card title="Contract Management">
          <p>Contract lifecycle management system.</p>
        </Card>
      ),
    },
    {
      key: 'compliance',
      label: (
        <span>
          <SafetyOutlined />
          {t('legal.compliance')}
        </span>
      ),
      children: (
        <Card title="Compliance Management">
          <p>Regulatory compliance tracking and management.</p>
        </Card>
      ),
    },
    {
      key: 'documents',
      label: (
        <span>
          <BookOutlined />
          {t('legal.documents')}
        </span>
      ),
      children: (
        <Card title="Legal Document Library">
          <p>Legal document management and templates.</p>
        </Card>
      ),
    },
  ];

  return (
    <LegalContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('legal.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />
    </LegalContainer>
  );
};

export default Legal;
