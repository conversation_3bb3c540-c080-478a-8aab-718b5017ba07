{"name": "token-types", "version": "6.0.0", "description": "Common token types for decoding and encoding numeric and string values", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}, "scripts": {"clean": "del-cli lib/**/*.js lib/***.js.map *.d.ts test/**/*.d.ts test/**/*.js test/**/*.js.map .nyc_output", "build": "npm run compile", "compile-src": "tsc --p lib", "compile-test": "tsc --p test", "compile": "npm run compile-src && npm run compile-test", "eslint": "eslint lib/**/*.ts --ignore-pattern lib/**/*.d.ts test/**/*.ts", "lint-ts": "tslint lib/index.ts --exclude '*.d.ts' 'test/**/*.ts' --exclude 'test/**/*.d.ts,lib/**/*.d.ts'", "lint-md": "remark -u preset-lint-recommended .", "lint": "npm run lint-md && npm run eslint", "test": "mocha", "test-coverage": "c8 npm run test", "send-codacy": "c8 report --reports-dir=./.coverage --reporter=text-lcov | codacy-coverage"}, "engines": {"node": ">=14.16"}, "repository": {"type": "git", "url": "https://github.com/Borewit/token-types"}, "files": ["lib/index.js", "lib/index.d.ts"], "license": "MIT", "type": "module", "exports": "./lib/index.js", "types": "lib/index.d.ts", "bugs": {"url": "https://github.com/Borewit/token-types/issues"}, "devDependencies": {"@types/chai": "^4.3.1", "@types/mocha": "^10.0.0", "@types/node": "^20.14.9", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.14.1", "c8": "^10.1.2", "chai": "^5.1.1", "del-cli": "^5.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsdoc": "^48.5.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-unicorn": "^54.0.0", "mocha": "^10.0.0", "remark-cli": "^12.0.1", "remark-preset-lint-recommended": "^7.0.0", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typescript": "^5.5.2"}, "dependencies": {"@tokenizer/token": "^0.3.0", "ieee754": "^1.2.1"}, "remarkConfig": {"plugins": ["preset-lint-recommended"]}, "keywords": ["token", "integer", "unsigned", "numeric", "float", "IEEE", "754", "strtok3"]}