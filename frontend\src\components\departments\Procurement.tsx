import React, { useState } from 'react';
import { Tabs, Typography, Table, Card, Row, Col, Statistic, Tag, Button, Space, Modal, Form, Input, Select, DatePicker } from 'antd';
import {
  DashboardOutlined,
  ShoppingOutlined,
  UserOutlined,
  FileTextOutlined,
  TruckOutlined,
  DollarOutlined,
  Bar<PERSON>hartOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import styled from 'styled-components';
import PurchaseOrderManagement from '../procurement/PurchaseOrderManagement';
import SupplierManagement from '../procurement/SupplierManagement';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ProcurementContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }
`;

interface PurchaseOrder {
  id: string;
  supplier: string;
  orderDate: string;
  deliveryDate: string;
  totalAmount: number;
  status: 'Draft' | 'Pending' | 'Approved' | 'Delivered' | 'Cancelled';
  items: number;
}

interface Supplier {
  id: string;
  name: string;
  contact: string;
  email: string;
  category: string;
  rating: number;
  totalOrders: number;
  status: 'Active' | 'Inactive';
}

const Procurement: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showPOModal, setShowPOModal] = useState(false);
  const [showSupplierModal, setShowSupplierModal] = useState(false);

  const purchaseOrders: PurchaseOrder[] = [
    {
      id: 'PO-001',
      supplier: 'ABC Supplies',
      orderDate: '2024-05-20',
      deliveryDate: '2024-05-25',
      totalAmount: 15000,
      status: 'Approved',
      items: 5,
    },
    {
      id: 'PO-002',
      supplier: 'XYZ Materials',
      orderDate: '2024-05-19',
      deliveryDate: '2024-05-24',
      totalAmount: 8500,
      status: 'Pending',
      items: 3,
    },
  ];

  const suppliers: Supplier[] = [
    {
      id: '1',
      name: 'ABC Supplies',
      contact: '+1234567890',
      email: '<EMAIL>',
      category: 'Office Supplies',
      rating: 4.5,
      totalOrders: 25,
      status: 'Active',
    },
    {
      id: '2',
      name: 'XYZ Materials',
      contact: '+1234567891',
      email: '<EMAIL>',
      category: 'Raw Materials',
      rating: 4.2,
      totalOrders: 18,
      status: 'Active',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Draft': return 'default';
      case 'Pending': return 'orange';
      case 'Approved': return 'blue';
      case 'Delivered': return 'green';
      case 'Cancelled': return 'red';
      default: return 'default';
    }
  };

  const poColumns: ColumnsType<PurchaseOrder> = [
    {
      title: 'PO Number',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'Supplier',
      dataIndex: 'supplier',
      key: 'supplier',
    },
    {
      title: 'Order Date',
      dataIndex: 'orderDate',
      key: 'orderDate',
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `$${amount.toLocaleString()}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" />
          <Button type="text" icon={<FileTextOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  const supplierColumns: ColumnsType<Supplier> = [
    {
      title: 'Supplier Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: 'Contact',
      dataIndex: 'contact',
      key: 'contact',
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: (rating) => `${rating}/5`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={status === 'Active' ? 'green' : 'red'}>{status}</Tag>,
    },
  ];

  const totalPOs = purchaseOrders.length;
  const pendingPOs = purchaseOrders.filter(po => po.status === 'Pending').length;
  const activeSuppliers = suppliers.filter(s => s.status === 'Active').length;
  const monthlySpend = purchaseOrders.reduce((sum, po) => sum + po.totalAmount, 0);

  const ProcurementDashboard = () => (
    <div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Purchase Orders"
              value={totalPOs}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Pending Approvals"
              value={pendingPOs}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Suppliers"
              value={activeSuppliers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Monthly Spend"
              value={monthlySpend}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#722ed1' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Recent Purchase Orders">
            <Table
              columns={poColumns}
              dataSource={purchaseOrders}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Top Suppliers">
            <Table
              columns={supplierColumns}
              dataSource={suppliers}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          {t('procurement.dashboard')}
        </span>
      ),
      children: <ProcurementDashboard />,
    },
    {
      key: 'purchase-orders',
      label: (
        <span>
          <FileTextOutlined />
          {t('procurement.purchaseOrders')}
        </span>
      ),
      children: <PurchaseOrderManagement />,
    },
    {
      key: 'suppliers',
      label: (
        <span>
          <UserOutlined />
          {t('procurement.suppliers')}
        </span>
      ),
      children: <SupplierManagement />,
    },
    {
      key: 'requisitions',
      label: (
        <span>
          <ShoppingOutlined />
          Requisitions
        </span>
      ),
      children: (
        <Card title="Purchase Requisitions">
          <p>Purchase requisition management system.</p>
        </Card>
      ),
    },
    {
      key: 'receiving',
      label: (
        <span>
          <TruckOutlined />
          Receiving
        </span>
      ),
      children: (
        <Card title="Goods Receiving">
          <p>Goods receiving and inspection system.</p>
        </Card>
      ),
    },
    {
      key: 'reports',
      label: (
        <span>
          <BarChartOutlined />
          Reports
        </span>
      ),
      children: (
        <Card title="Procurement Reports">
          <p>Procurement analytics and reporting system.</p>
        </Card>
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Settings
        </span>
      ),
      children: (
        <Card title="Procurement Settings">
          <p>Procurement system configuration and settings.</p>
        </Card>
      ),
    },
  ];

  return (
    <ProcurementContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('procurement.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />

      {/* Purchase Order Modal */}
      <Modal
        title="Create Purchase Order"
        open={showPOModal}
        onCancel={() => setShowPOModal(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item name="supplier" label="Supplier" rules={[{ required: true }]}>
            <Select placeholder="Select supplier">
              {suppliers.map(supplier => (
                <Option key={supplier.id} value={supplier.name}>{supplier.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="deliveryDate" label="Delivery Date" rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name="notes" label="Notes">
            <TextArea rows={3} placeholder="Enter order notes" />
          </Form.Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowPOModal(false)}>Cancel</Button>
              <Button type="primary">Create Order</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Supplier Modal */}
      <Modal
        title="Add Supplier"
        open={showSupplierModal}
        onCancel={() => setShowSupplierModal(false)}
        footer={null}
      >
        <Form layout="vertical">
          <Form.Item name="name" label="Supplier Name" rules={[{ required: true }]}>
            <Input placeholder="Enter supplier name" />
          </Form.Item>
          <Form.Item name="email" label="Email" rules={[{ required: true, type: 'email' }]}>
            <Input placeholder="Enter email address" />
          </Form.Item>
          <Form.Item name="contact" label="Contact Number">
            <Input placeholder="Enter contact number" />
          </Form.Item>
          <Form.Item name="category" label="Category">
            <Select placeholder="Select category">
              <Option value="Office Supplies">Office Supplies</Option>
              <Option value="Raw Materials">Raw Materials</Option>
              <Option value="Equipment">Equipment</Option>
            </Select>
          </Form.Item>
          <Form.Item style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowSupplierModal(false)}>Cancel</Button>
              <Button type="primary">Add Supplier</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </ProcurementContainer>
  );
};

export default Procurement;
