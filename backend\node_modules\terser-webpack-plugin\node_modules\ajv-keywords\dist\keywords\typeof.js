"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const typeof_1 = __importDefault(require("../definitions/typeof"));
const typeofPlugin = (ajv) => ajv.addKeyword((0, typeof_1.default)());
exports.default = typeofPlugin;
module.exports = typeofPlugin;
//# sourceMappingURL=typeof.js.map