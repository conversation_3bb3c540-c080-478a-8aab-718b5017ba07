import React, { useState, useEffect } from 'react';
import { Tabs, Typography, Table, Card, Row, Col, Statistic, Tag, Avatar, Spin, Button, Space, Input, Select, Modal, Form, message, DatePicker, InputNumber } from 'antd';
import {
  DashboardOutlined,
  TeamOutlined,
  CalendarOutlined,
  DollarOutlined,
  TrophyOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  FilterOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { hrAPI } from '../../services/api';
import AttendanceManagement from '../hr/AttendanceManagement';
import LeaveManagement from '../hr/LeaveManagement';

const { Title } = Typography;

const HRContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }
`;

interface Employee {
  id: string;
  name: string;
  position: string;
  department: string;
  email: string;
  hireDate: string;
  salary: number;
  status: 'Active' | 'On Leave' | 'Inactive';
}

const HumanResources: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState<string>('');

  // Load employees from API
  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    try {
      setLoading(true);
      const response = await hrAPI.getEmployees({
        page: 1,
        limit: 100,
        department: departmentFilter || undefined,
      });

      if (response && response.data) {
        setEmployees(response.data.data || []);
      }
    } catch (error) {
      console.error('Error loading employees:', error);
      message.error('Failed to load employees');
      // No fallback data - keep empty for production
      setEmployees([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateEmployee = async (values: any) => {
    try {
      setLoading(true);
      const employeeData = {
        ...values,
        hireDate: values.hireDate?.format('YYYY-MM-DD'),
      };

      const response = await hrAPI.createEmployee(employeeData);

      if (response && response.success) {
        message.success('Employee created successfully');
        setShowCreateModal(false);
        form.resetFields();
        loadEmployees();
      }
    } catch (error) {
      console.error('Error creating employee:', error);
      message.error('Failed to create employee');
      // Production mode - no fallback data
      setShowCreateModal(false);
      form.resetFields();
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'green';
      case 'On Leave': return 'orange';
      case 'Inactive': return 'red';
      default: return 'default';
    }
  };

  const employeeColumns: ColumnsType<Employee> = [
    {
      title: 'Employee',
      key: 'employee',
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar icon={<UserOutlined />} style={{ marginRight: 12 }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ fontSize: '12px', color: '#666' }}>{record.position}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: 'Hire Date',
      dataIndex: 'hireDate',
      key: 'hireDate',
    },
    {
      title: 'Salary',
      dataIndex: 'salary',
      key: 'salary',
      render: (salary) => `$${salary.toLocaleString()}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
  ];

  const totalEmployees = employees.length;
  const activeEmployees = employees.filter(emp => emp.status === 'Active').length;
  const onLeaveEmployees = employees.filter(emp => emp.status === 'On Leave').length;

  const HRDashboard = () => (
    <div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Employees"
              value={totalEmployees}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Employees"
              value={activeEmployees}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="On Leave"
              value={onLeaveEmployees}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="New Hires (Month)"
              value={2}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="Employee Overview">
        <Table
          columns={employeeColumns}
          dataSource={employees}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          Dashboard
        </span>
      ),
      children: <HRDashboard />,
    },
    {
      key: 'employees',
      label: (
        <span>
          <TeamOutlined />
          Employees
        </span>
      ),
      children: (
        <Card title="Employee Management">
          <Table columns={employeeColumns} dataSource={employees} rowKey="id" />
        </Card>
      ),
    },
    {
      key: 'attendance',
      label: (
        <span>
          <ClockCircleOutlined />
          {t('hr.attendance')}
        </span>
      ),
      children: <AttendanceManagement />,
    },
    {
      key: 'leave',
      label: (
        <span>
          <CalendarOutlined />
          {t('hr.leaveManagement')}
        </span>
      ),
      children: <LeaveManagement />,
    },
    {
      key: 'payroll',
      label: (
        <span>
          <DollarOutlined />
          Payroll
        </span>
      ),
      children: (
        <Card title="Payroll Management">
          <p>Employee payroll processing and salary management system.</p>
        </Card>
      ),
    },
    {
      key: 'performance',
      label: (
        <span>
          <TrophyOutlined />
          Performance
        </span>
      ),
      children: (
        <Card title="Performance Management">
          <p>Employee performance evaluation and review system.</p>
        </Card>
      ),
    },
    {
      key: 'reports',
      label: (
        <span>
          <FileTextOutlined />
          Reports
        </span>
      ),
      children: (
        <Card title="HR Reports">
          <p>Human resources analytics and reporting system.</p>
        </Card>
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Settings
        </span>
      ),
      children: (
        <Card title="HR Settings">
          <p>Human resources system configuration and settings.</p>
        </Card>
      ),
    },
  ];

  return (
    <HRContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('hr.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />
    </HRContainer>
  );
};

export default HumanResources;
