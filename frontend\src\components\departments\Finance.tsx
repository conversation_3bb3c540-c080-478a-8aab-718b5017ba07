import React, { useState } from 'react';
import { Tabs, Typography, Card, Row, Col, Statistic, Table, Tag, Button } from 'antd';
import {
  DashboardOutlined,
  CalculatorOutlined,
  FileTextOutlined,
  BarChartOutlined,
  BankOutlined,
  CreditCardOutlined,
  DollarOutlined,
  Pie<PERSON>hartOutlined,
  AuditOutlined,
  SettingOutlined,
  TrophyOutlined,
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import type { ColumnsType } from 'antd/es/table';
import AccountingLedger from '../finance/AccountingLedger';
import BudgetManagement from '../finance/BudgetManagement';

const { Title } = Typography;

const FinanceContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }

  .ant-tabs-tab {
    padding: 12px 16px;
  }
`;

interface Transaction {
  id: string;
  type: 'income' | 'expense';
  description: string;
  amount: number;
  date: string;
  category: string;
}

const Finance: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');

  const financialStats = {
    totalRevenue: 485000,
    totalExpenses: 325000,
    netProfit: 160000,
    cashFlow: 75000,
    accountsReceivable: 125000,
    accountsPayable: 85000,
  };

  const recentTransactions: Transaction[] = [
    {
      id: '1',
      type: 'income',
      description: 'Client Payment - ABC Corp',
      amount: 15000,
      date: '2024-05-20',
      category: 'Revenue',
    },
    {
      id: '2',
      type: 'expense',
      description: 'Office Rent Payment',
      amount: 3500,
      date: '2024-05-19',
      category: 'Operating Expenses',
    },
    {
      id: '3',
      type: 'income',
      description: 'Product Sales',
      amount: 8750,
      date: '2024-05-18',
      category: 'Revenue',
    },
  ];

  const transactionColumns: ColumnsType<Transaction> = [
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={type === 'income' ? 'green' : 'red'}>
          {type === 'income' ? 'Income' : 'Expense'}
        </Tag>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount, record) => (
        <span style={{ color: record.type === 'income' ? '#52c41a' : '#f5222d' }}>
          {record.type === 'income' ? '+' : '-'}${amount.toLocaleString()}
        </span>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
    },
  ];

  const FinanceDashboard = () => (
    <div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Revenue"
              value={financialStats.totalRevenue}
              prefix={<DollarOutlined />}
              precision={0}
              valueStyle={{ color: '#52c41a' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Expenses"
              value={financialStats.totalExpenses}
              prefix={<CreditCardOutlined />}
              precision={0}
              valueStyle={{ color: '#f5222d' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Net Profit"
              value={financialStats.netProfit}
              prefix={<TrophyOutlined />}
              precision={0}
              valueStyle={{ color: '#722ed1' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Cash Flow"
              value={financialStats.cashFlow}
              prefix={<BankOutlined />}
              precision={0}
              valueStyle={{ color: '#1890ff' }}
              formatter={(value) => `$${value?.toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      <Card title="Recent Transactions" extra={<Button type="link">View All</Button>}>
        <Table
          columns={transactionColumns}
          dataSource={recentTransactions}
          rowKey="id"
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          {t('finance.dashboard')}
        </span>
      ),
      children: <FinanceDashboard />,
    },
    {
      key: 'accounting',
      label: (
        <span>
          <CalculatorOutlined />
          {t('finance.accounting')}
        </span>
      ),
      children: <AccountingLedger />,
    },
    {
      key: 'budget',
      label: (
        <span>
          <DollarOutlined />
          {t('finance.budget')}
        </span>
      ),
      children: <BudgetManagement />,
    },
    {
      key: 'reports',
      label: (
        <span>
          <BarChartOutlined />
          Reports
        </span>
      ),
      children: (
        <Card title="Financial Reports">
          <p>P&L statements, balance sheets, and financial analytics.</p>
        </Card>
      ),
    },
    {
      key: 'bank',
      label: (
        <span>
          <BankOutlined />
          Bank Reconciliation
        </span>
      ),
      children: (
        <Card title="Bank Reconciliation">
          <p>Bank account reconciliation and cash management system.</p>
        </Card>
      ),
    },
    {
      key: 'expenses',
      label: (
        <span>
          <CreditCardOutlined />
          Expenses
        </span>
      ),
      children: (
        <Card title="Expense Management">
          <p>Expense tracking, approval workflows, and reimbursement system.</p>
        </Card>
      ),
    },
    {
      key: 'cashflow',
      label: (
        <span>
          <PieChartOutlined />
          Cash Flow
        </span>
      ),
      children: (
        <Card title="Cash Flow Management">
          <p>Cash flow analysis, forecasting, and liquidity management.</p>
        </Card>
      ),
    },
    {
      key: 'tax',
      label: (
        <span>
          <FileTextOutlined />
          Tax Management
        </span>
      ),
      children: (
        <Card title="Tax Management">
          <p>Tax calculations, filing, and compliance management system.</p>
        </Card>
      ),
    },
    {
      key: 'analysis',
      label: (
        <span>
          <AuditOutlined />
          Analysis
        </span>
      ),
      children: (
        <Card title="Financial Analysis">
          <p>Financial ratios, trend analysis, and performance metrics.</p>
        </Card>
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Settings
        </span>
      ),
      children: (
        <Card title="Finance Settings">
          <p>Financial system configuration and accounting preferences.</p>
        </Card>
      ),
    },
  ];

  return (
    <FinanceContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('finance.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />
    </FinanceContainer>
  );
};

export default Finance;
