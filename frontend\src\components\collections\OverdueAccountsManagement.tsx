import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  Divider,
  DatePicker,
  InputNumber,
  Progress,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  PhoneOutlined,
  MailOutlined,
  DollarOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface OverdueAccount {
  id: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  amount: number;
  daysOverdue: number;
  lastContact: string;
  contactMethod: 'email' | 'phone' | 'letter';
  status: 'new' | 'contacted' | 'promised' | 'legal' | 'written-off';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  notes: string;
  assignedTo: string;
}

interface CollectionAction {
  id: string;
  accountId: string;
  date: string;
  type: 'email' | 'phone' | 'letter' | 'meeting';
  description: string;
  outcome: string;
  nextAction: string;
  nextActionDate: string;
}

const OverdueAccountsManagement: React.FC = () => {
  const [accounts, setAccounts] = useState<OverdueAccount[]>([
    {
      id: '1',
      customerName: 'ABC Corporation',
      customerEmail: '<EMAIL>',
      customerPhone: '******-0123',
      invoiceNumber: 'INV-2025-001',
      invoiceDate: '2024-12-15',
      dueDate: '2025-01-15',
      amount: 15000,
      daysOverdue: 45,
      lastContact: '2025-01-20',
      contactMethod: 'email',
      status: 'contacted',
      priority: 'high',
      notes: 'Customer requested payment plan',
      assignedTo: 'John Smith',
    },
    {
      id: '2',
      customerName: 'XYZ Limited',
      customerEmail: '<EMAIL>',
      customerPhone: '******-0456',
      invoiceNumber: 'INV-2025-002',
      invoiceDate: '2024-12-20',
      dueDate: '2025-01-20',
      amount: 8500,
      daysOverdue: 30,
      lastContact: '2025-01-25',
      contactMethod: 'phone',
      status: 'promised',
      priority: 'medium',
      notes: 'Payment promised by end of week',
      assignedTo: 'Sarah Johnson',
    },
  ]);

  const [actions, setActions] = useState<CollectionAction[]>([]);
  const [showActionModal, setShowActionModal] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<OverdueAccount | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [form] = Form.useForm();

  const getStatusColor = (status: string) => {
    const colors = {
      'new': 'red',
      'contacted': 'orange',
      'promised': 'blue',
      'legal': 'purple',
      'written-off': 'gray',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'green',
      'medium': 'orange',
      'high': 'red',
      'urgent': 'magenta',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const filteredAccounts = accounts.filter(account => {
    const matchesSearch = account.customerName.toLowerCase().includes(searchText.toLowerCase()) ||
                         account.invoiceNumber.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || account.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || account.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const handleAddAction = async (values: any) => {
    try {
      const newAction: CollectionAction = {
        id: (actions.length + 1).toString(),
        accountId: selectedAccount!.id,
        date: values.date.format('YYYY-MM-DD'),
        type: values.type,
        description: values.description,
        outcome: values.outcome,
        nextAction: values.nextAction,
        nextActionDate: values.nextActionDate?.format('YYYY-MM-DD') || '',
      };

      setActions([newAction, ...actions]);
      
      // Update account status and last contact
      setAccounts(prev => prev.map(account => 
        account.id === selectedAccount!.id 
          ? { 
              ...account, 
              lastContact: values.date.format('YYYY-MM-DD'),
              contactMethod: values.type,
              status: values.newStatus || account.status,
              notes: values.notes || account.notes,
            }
          : account
      ));

      setShowActionModal(false);
      form.resetFields();
      setSelectedAccount(null);
      message.success('Collection action recorded successfully!');
    } catch (error) {
      message.error('Failed to record collection action');
    }
  };

  const columns = [
    {
      title: 'Customer',
      key: 'customer',
      width: 200,
      render: (_, record: OverdueAccount) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.customerName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.customerEmail}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.customerPhone}
          </div>
        </div>
      ),
    },
    {
      title: 'Invoice',
      key: 'invoice',
      width: 150,
      render: (_, record: OverdueAccount) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.invoiceNumber}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            Due: {dayjs(record.dueDate).format('MMM DD, YYYY')}
          </div>
        </div>
      ),
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold', color: '#f5222d' }}>
          ${amount.toLocaleString()}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Days Overdue',
      dataIndex: 'daysOverdue',
      key: 'daysOverdue',
      width: 120,
      render: (days: number) => (
        <Badge
          count={days}
          style={{ 
            backgroundColor: days > 60 ? '#f5222d' : days > 30 ? '#fa8c16' : '#52c41a' 
          }}
        />
      ),
      align: 'center' as const,
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.replace('-', ' ').toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Last Contact',
      key: 'lastContact',
      width: 150,
      render: (_, record: OverdueAccount) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            {dayjs(record.lastContact).format('MMM DD, YYYY')}
          </div>
          <Tag size="small" color="blue">
            {record.contactMethod.toUpperCase()}
          </Tag>
        </div>
      ),
    },
    {
      title: 'Assigned To',
      dataIndex: 'assignedTo',
      key: 'assignedTo',
      width: 120,
      render: (assignedTo: string) => (
        <Tag color="purple">{assignedTo}</Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: OverdueAccount) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Call Customer">
            <Button 
              type="text" 
              icon={<PhoneOutlined />} 
              size="small"
              onClick={() => window.open(`tel:${record.customerPhone}`)}
            />
          </Tooltip>
          <Tooltip title="Email Customer">
            <Button 
              type="text" 
              icon={<MailOutlined />} 
              size="small"
              onClick={() => window.open(`mailto:${record.customerEmail}`)}
            />
          </Tooltip>
          <Tooltip title="Record Action">
            <Button 
              type="text" 
              icon={<PlusOutlined />} 
              size="small"
              onClick={() => {
                setSelectedAccount(record);
                setShowActionModal(true);
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const totalOverdue = filteredAccounts.reduce((sum, account) => sum + account.amount, 0);
  const avgDaysOverdue = filteredAccounts.length > 0 
    ? filteredAccounts.reduce((sum, account) => sum + account.daysOverdue, 0) / filteredAccounts.length 
    : 0;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Summary Cards */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Total Overdue</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#f5222d' }}>
                      ${totalOverdue.toLocaleString()}
                    </div>
                  </div>
                  <DollarOutlined style={{ fontSize: '32px', color: '#f5222d' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Overdue Accounts</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                      {filteredAccounts.length}
                    </div>
                  </div>
                  <ExclamationCircleOutlined style={{ fontSize: '32px', color: '#fa8c16' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Avg Days Overdue</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                      {avgDaysOverdue.toFixed(0)}
                    </div>
                  </div>
                  <CalendarOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Overdue Accounts Table */}
        <Col xs={24}>
          <Card
            title="Overdue Accounts Management"
            extra={
              <Space>
                <Button type="primary" icon={<PlusOutlined />}>
                  Add Manual Entry
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search customers or invoices..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="new">New</Select.Option>
                  <Select.Option value="contacted">Contacted</Select.Option>
                  <Select.Option value="promised">Promised</Select.Option>
                  <Select.Option value="legal">Legal Action</Select.Option>
                  <Select.Option value="written-off">Written Off</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by priority"
                  style={{ width: '100%' }}
                  value={priorityFilter}
                  onChange={setPriorityFilter}
                >
                  <Select.Option value="all">All Priorities</Select.Option>
                  <Select.Option value="low">Low</Select.Option>
                  <Select.Option value="medium">Medium</Select.Option>
                  <Select.Option value="high">High</Select.Option>
                  <Select.Option value="urgent">Urgent</Select.Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredAccounts}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} accounts`,
              }}
              scroll={{ x: 1400 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Record Action Modal */}
      <Modal
        title={`Record Collection Action - ${selectedAccount?.customerName}`}
        open={showActionModal}
        onCancel={() => {
          setShowActionModal(false);
          setSelectedAccount(null);
          form.resetFields();
        }}
        footer={null}
        width={700}
      >
        <Form form={form} layout="vertical" onFinish={handleAddAction}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="date"
                label="Contact Date"
                rules={[{ required: true, message: 'Please select date' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="type"
                label="Contact Method"
                rules={[{ required: true, message: 'Please select contact method' }]}
              >
                <Select placeholder="Select method">
                  <Select.Option value="email">Email</Select.Option>
                  <Select.Option value="phone">Phone Call</Select.Option>
                  <Select.Option value="letter">Letter</Select.Option>
                  <Select.Option value="meeting">In-Person Meeting</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="description"
                label="Action Description"
                rules={[{ required: true, message: 'Please enter description' }]}
              >
                <Input.TextArea rows={3} placeholder="Describe the collection action taken..." />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="outcome"
                label="Outcome"
                rules={[{ required: true, message: 'Please enter outcome' }]}
              >
                <Input.TextArea rows={2} placeholder="What was the result of this contact?" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="newStatus" label="Update Status">
                <Select placeholder="Update account status">
                  <Select.Option value="contacted">Contacted</Select.Option>
                  <Select.Option value="promised">Payment Promised</Select.Option>
                  <Select.Option value="legal">Legal Action Required</Select.Option>
                  <Select.Option value="written-off">Write Off</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item name="nextActionDate" label="Next Action Date">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="nextAction" label="Next Action Required">
                <Input placeholder="What should be done next?" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowActionModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Record Action</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default OverdueAccountsManagement;
