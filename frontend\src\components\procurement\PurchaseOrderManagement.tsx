import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  Divider,
  DatePicker,
  InputNumber,
  Steps,
  Tooltip,
  Badge,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PrinterOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  DollarOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Step } = Steps;

interface PurchaseOrderItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
  specifications?: string;
}

interface PurchaseOrder {
  id: string;
  poNumber: string;
  supplier: {
    id: string;
    name: string;
    email: string;
    phone: string;
  };
  orderDate: string;
  deliveryDate: string;
  expectedDelivery: string;
  totalAmount: number;
  status: 'draft' | 'pending' | 'approved' | 'ordered' | 'received' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  items: PurchaseOrderItem[];
  notes: string;
  approvedBy?: string;
  approvedDate?: string;
  createdBy: string;
  department: string;
  paymentTerms: string;
  shippingAddress: string;
}

const PurchaseOrderManagement: React.FC = () => {
  const [orders, setOrders] = useState<PurchaseOrder[]>([
    {
      id: '1',
      poNumber: 'PO-2025-001',
      supplier: {
        id: '1',
        name: 'ABC Office Supplies',
        email: '<EMAIL>',
        phone: '******-0123',
      },
      orderDate: '2025-01-15',
      deliveryDate: '2025-01-25',
      expectedDelivery: '2025-01-25',
      totalAmount: 15750,
      status: 'approved',
      priority: 'medium',
      items: [
        {
          id: '1',
          description: 'Office Chairs - Ergonomic',
          quantity: 10,
          unitPrice: 250,
          total: 2500,
          specifications: 'Black leather, adjustable height',
        },
        {
          id: '2',
          description: 'Desk Lamps - LED',
          quantity: 15,
          unitPrice: 85,
          total: 1275,
          specifications: 'White finish, adjustable arm',
        },
      ],
      notes: 'Urgent delivery required for new office setup',
      approvedBy: 'John Manager',
      approvedDate: '2025-01-16',
      createdBy: 'Sarah Procurement',
      department: 'Administration',
      paymentTerms: 'Net 30',
      shippingAddress: '123 Business St, City, State 12345',
    },
    {
      id: '2',
      poNumber: 'PO-2025-002',
      supplier: {
        id: '2',
        name: 'Tech Solutions Ltd',
        email: '<EMAIL>',
        phone: '******-0456',
      },
      orderDate: '2025-01-18',
      deliveryDate: '2025-02-01',
      expectedDelivery: '2025-02-01',
      totalAmount: 8500,
      status: 'pending',
      priority: 'high',
      items: [
        {
          id: '3',
          description: 'Laptops - Business Grade',
          quantity: 5,
          unitPrice: 1200,
          total: 6000,
          specifications: 'Intel i7, 16GB RAM, 512GB SSD',
        },
      ],
      notes: 'For new employee onboarding',
      createdBy: 'Mike IT',
      department: 'IT',
      paymentTerms: 'Net 15',
      shippingAddress: '123 Business St, City, State 12345',
    },
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<PurchaseOrder | null>(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [form] = Form.useForm();

  const getStatusColor = (status: string) => {
    const colors = {
      'draft': 'default',
      'pending': 'orange',
      'approved': 'blue',
      'ordered': 'cyan',
      'received': 'green',
      'cancelled': 'red',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'green',
      'medium': 'orange',
      'high': 'red',
      'urgent': 'magenta',
    };
    return colors[priority as keyof typeof colors] || 'default';
  };

  const getStatusStep = (status: string) => {
    const steps = {
      'draft': 0,
      'pending': 1,
      'approved': 2,
      'ordered': 3,
      'received': 4,
      'cancelled': -1,
    };
    return steps[status as keyof typeof steps] || 0;
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.poNumber.toLowerCase().includes(searchText.toLowerCase()) ||
                         order.supplier.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         order.department.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesPriority = priorityFilter === 'all' || order.priority === priorityFilter;
    return matchesSearch && matchesStatus && matchesPriority;
  });

  const handleCreateOrder = async (values: any) => {
    try {
      const newOrder: PurchaseOrder = {
        id: (orders.length + 1).toString(),
        poNumber: `PO-2025-${String(orders.length + 1).padStart(3, '0')}`,
        supplier: {
          id: values.supplierId,
          name: values.supplierName,
          email: values.supplierEmail || '',
          phone: values.supplierPhone || '',
        },
        orderDate: values.orderDate.format('YYYY-MM-DD'),
        deliveryDate: values.deliveryDate.format('YYYY-MM-DD'),
        expectedDelivery: values.deliveryDate.format('YYYY-MM-DD'),
        totalAmount: values.totalAmount || 0,
        status: 'draft',
        priority: values.priority,
        items: [],
        notes: values.notes || '',
        createdBy: 'Current User',
        department: values.department,
        paymentTerms: values.paymentTerms,
        shippingAddress: values.shippingAddress,
      };

      setOrders([newOrder, ...orders]);
      setShowCreateModal(false);
      form.resetFields();
      message.success('Purchase order created successfully!');
    } catch (error) {
      message.error('Failed to create purchase order');
    }
  };

  const handleStatusUpdate = (orderId: string, newStatus: string) => {
    setOrders(prev => prev.map(order => 
      order.id === orderId 
        ? { 
            ...order, 
            status: newStatus as any,
            ...(newStatus === 'approved' && {
              approvedBy: 'Current User',
              approvedDate: dayjs().format('YYYY-MM-DD'),
            })
          }
        : order
    ));
    message.success(`Purchase order ${newStatus} successfully!`);
  };

  const columns = [
    {
      title: 'PO Number',
      dataIndex: 'poNumber',
      key: 'poNumber',
      width: 130,
      render: (poNumber: string) => (
        <span style={{ fontWeight: 'bold', color: '#1890ff' }}>
          {poNumber}
        </span>
      ),
    },
    {
      title: 'Supplier',
      key: 'supplier',
      width: 200,
      render: (_, record: PurchaseOrder) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.supplier.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.supplier.email}
          </div>
        </div>
      ),
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      width: 120,
      render: (dept: string) => <Tag color="blue">{dept}</Tag>,
    },
    {
      title: 'Order Date',
      dataIndex: 'orderDate',
      key: 'orderDate',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
    },
    {
      title: 'Delivery Date',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
    },
    {
      title: 'Amount',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => (
        <span style={{ fontWeight: 'bold' }}>
          ${amount.toLocaleString()}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Priority',
      dataIndex: 'priority',
      key: 'priority',
      width: 100,
      render: (priority: string) => (
        <Tag color={getPriorityColor(priority)}>
          {priority.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Progress',
      key: 'progress',
      width: 150,
      render: (_, record: PurchaseOrder) => {
        const step = getStatusStep(record.status);
        const percent = step >= 0 ? (step / 4) * 100 : 0;
        return (
          <Progress
            percent={percent}
            size="small"
            strokeColor={record.status === 'cancelled' ? '#f5222d' : '#52c41a'}
            showInfo={false}
          />
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 200,
      render: (_, record: PurchaseOrder) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => {
                setSelectedOrder(record);
                setShowViewModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button type="text" icon={<EditOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Print">
            <Button type="text" icon={<PrinterOutlined />} size="small" />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="Approve">
              <Button 
                type="text" 
                icon={<CheckCircleOutlined />} 
                size="small"
                style={{ color: '#52c41a' }}
                onClick={() => handleStatusUpdate(record.id, 'approved')}
              />
            </Tooltip>
          )}
          {record.status === 'approved' && (
            <Tooltip title="Mark as Ordered">
              <Button 
                type="text" 
                icon={<FileTextOutlined />} 
                size="small"
                style={{ color: '#1890ff' }}
                onClick={() => handleStatusUpdate(record.id, 'ordered')}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  const totalOrders = filteredOrders.length;
  const totalValue = filteredOrders.reduce((sum, order) => sum + order.totalAmount, 0);
  const pendingApprovals = filteredOrders.filter(order => order.status === 'pending').length;
  const avgOrderValue = totalOrders > 0 ? totalValue / totalOrders : 0;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Summary Cards */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Total Orders</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff' }}>
                      {totalOrders}
                    </div>
                  </div>
                  <FileTextOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Total Value</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                      ${totalValue.toLocaleString()}
                    </div>
                  </div>
                  <DollarOutlined style={{ fontSize: '32px', color: '#52c41a' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Pending Approvals</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#fa8c16' }}>
                      {pendingApprovals}
                    </div>
                  </div>
                  <ClockCircleOutlined style={{ fontSize: '32px', color: '#fa8c16' }} />
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <div style={{ fontSize: '14px', color: '#666' }}>Avg Order Value</div>
                    <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#722ed1' }}>
                      ${avgOrderValue.toLocaleString()}
                    </div>
                  </div>
                  <ExclamationCircleOutlined style={{ fontSize: '32px', color: '#722ed1' }} />
                </div>
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Purchase Orders Table */}
        <Col xs={24}>
          <Card
            title="Purchase Orders Management"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowCreateModal(true)}
                >
                  Create Purchase Order
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search PO number, supplier, department..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="draft">Draft</Select.Option>
                  <Select.Option value="pending">Pending</Select.Option>
                  <Select.Option value="approved">Approved</Select.Option>
                  <Select.Option value="ordered">Ordered</Select.Option>
                  <Select.Option value="received">Received</Select.Option>
                  <Select.Option value="cancelled">Cancelled</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by priority"
                  style={{ width: '100%' }}
                  value={priorityFilter}
                  onChange={setPriorityFilter}
                >
                  <Select.Option value="all">All Priorities</Select.Option>
                  <Select.Option value="low">Low</Select.Option>
                  <Select.Option value="medium">Medium</Select.Option>
                  <Select.Option value="high">High</Select.Option>
                  <Select.Option value="urgent">Urgent</Select.Option>
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredOrders}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} purchase orders`,
              }}
              scroll={{ x: 1400 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Create Purchase Order Modal */}
      <Modal
        title="Create Purchase Order"
        open={showCreateModal}
        onCancel={() => {
          setShowCreateModal(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateOrder}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="supplierName"
                label="Supplier"
                rules={[{ required: true, message: 'Please enter supplier name' }]}
              >
                <Input placeholder="Enter supplier name" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="department"
                label="Department"
                rules={[{ required: true, message: 'Please select department' }]}
              >
                <Select placeholder="Select department">
                  <Select.Option value="Administration">Administration</Select.Option>
                  <Select.Option value="IT">IT</Select.Option>
                  <Select.Option value="Marketing">Marketing</Select.Option>
                  <Select.Option value="Finance">Finance</Select.Option>
                  <Select.Option value="HR">HR</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="orderDate"
                label="Order Date"
                rules={[{ required: true, message: 'Please select order date' }]}
                initialValue={dayjs()}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="deliveryDate"
                label="Expected Delivery Date"
                rules={[{ required: true, message: 'Please select delivery date' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="priority"
                label="Priority"
                rules={[{ required: true, message: 'Please select priority' }]}
              >
                <Select placeholder="Select priority">
                  <Select.Option value="low">Low</Select.Option>
                  <Select.Option value="medium">Medium</Select.Option>
                  <Select.Option value="high">High</Select.Option>
                  <Select.Option value="urgent">Urgent</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="paymentTerms"
                label="Payment Terms"
                rules={[{ required: true, message: 'Please select payment terms' }]}
              >
                <Select placeholder="Select payment terms">
                  <Select.Option value="Net 15">Net 15</Select.Option>
                  <Select.Option value="Net 30">Net 30</Select.Option>
                  <Select.Option value="Net 60">Net 60</Select.Option>
                  <Select.Option value="COD">Cash on Delivery</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="shippingAddress"
                label="Shipping Address"
                rules={[{ required: true, message: 'Please enter shipping address' }]}
              >
                <Input.TextArea rows={2} placeholder="Enter complete shipping address" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="notes" label="Notes">
                <Input.TextArea rows={3} placeholder="Enter any special instructions or notes" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowCreateModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Create Purchase Order</Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* View Purchase Order Modal */}
      <Modal
        title={`Purchase Order Details - ${selectedOrder?.poNumber}`}
        open={showViewModal}
        onCancel={() => {
          setShowViewModal(false);
          setSelectedOrder(null);
        }}
        footer={null}
        width={900}
      >
        {selectedOrder && (
          <div>
            <Row gutter={[24, 24]}>
              <Col xs={24}>
                <Steps current={getStatusStep(selectedOrder.status)} size="small">
                  <Step title="Draft" icon={<EditOutlined />} />
                  <Step title="Pending" icon={<ClockCircleOutlined />} />
                  <Step title="Approved" icon={<CheckCircleOutlined />} />
                  <Step title="Ordered" icon={<FileTextOutlined />} />
                  <Step title="Received" icon={<CheckCircleOutlined />} />
                </Steps>
              </Col>
              
              <Col xs={24} sm={12}>
                <Card size="small" title="Order Information">
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>PO Number: </Text>
                    <Text>{selectedOrder.poNumber}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Order Date: </Text>
                    <Text>{dayjs(selectedOrder.orderDate).format('MMM DD, YYYY')}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Delivery Date: </Text>
                    <Text>{dayjs(selectedOrder.deliveryDate).format('MMM DD, YYYY')}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Department: </Text>
                    <Tag color="blue">{selectedOrder.department}</Tag>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Priority: </Text>
                    <Tag color={getPriorityColor(selectedOrder.priority)}>
                      {selectedOrder.priority.toUpperCase()}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Status: </Text>
                    <Tag color={getStatusColor(selectedOrder.status)}>
                      {selectedOrder.status.toUpperCase()}
                    </Tag>
                  </div>
                </Card>
              </Col>

              <Col xs={24} sm={12}>
                <Card size="small" title="Supplier Information">
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Name: </Text>
                    <Text>{selectedOrder.supplier.name}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Email: </Text>
                    <Text>{selectedOrder.supplier.email}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Phone: </Text>
                    <Text>{selectedOrder.supplier.phone}</Text>
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <Text strong>Payment Terms: </Text>
                    <Text>{selectedOrder.paymentTerms}</Text>
                  </div>
                </Card>
              </Col>

              <Col xs={24}>
                <Card size="small" title="Order Items">
                  <Table
                    dataSource={selectedOrder.items}
                    rowKey="id"
                    pagination={false}
                    size="small"
                    columns={[
                      {
                        title: 'Description',
                        dataIndex: 'description',
                        key: 'description',
                      },
                      {
                        title: 'Specifications',
                        dataIndex: 'specifications',
                        key: 'specifications',
                      },
                      {
                        title: 'Quantity',
                        dataIndex: 'quantity',
                        key: 'quantity',
                        width: 100,
                        align: 'center' as const,
                      },
                      {
                        title: 'Unit Price',
                        dataIndex: 'unitPrice',
                        key: 'unitPrice',
                        width: 120,
                        render: (price: number) => `$${price.toLocaleString()}`,
                        align: 'right' as const,
                      },
                      {
                        title: 'Total',
                        dataIndex: 'total',
                        key: 'total',
                        width: 120,
                        render: (total: number) => (
                          <span style={{ fontWeight: 'bold' }}>
                            ${total.toLocaleString()}
                          </span>
                        ),
                        align: 'right' as const,
                      },
                    ]}
                  />
                  <Divider />
                  <div style={{ textAlign: 'right' }}>
                    <Text strong style={{ fontSize: '16px' }}>
                      Total Amount: ${selectedOrder.totalAmount.toLocaleString()}
                    </Text>
                  </div>
                </Card>
              </Col>

              {selectedOrder.notes && (
                <Col xs={24}>
                  <Card size="small" title="Notes">
                    <Text>{selectedOrder.notes}</Text>
                  </Card>
                </Col>
              )}
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PurchaseOrderManagement;
