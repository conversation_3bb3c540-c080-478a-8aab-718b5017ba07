import React, { useState } from 'react';
import { Tabs, Typography, Table, Card, Row, Col, Statistic, Tag, Button, Space, Modal, Form, Input, Select, InputNumber, Progress } from 'antd';
import {
  DashboardOutlined,
  InboxOutlined,
  BarChartOutlined,
  SyncOutlined,
  AlertOutlined,
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  MinusCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import ProductManagement from '../inventory/ProductManagement';
import StockMovements from '../inventory/StockMovements';

const { Title } = Typography;
const { Option } = Select;

const InventoryContainer = styled.div`
  .ant-tabs-content-holder {
    padding: 0;
  }
`;

interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unitPrice: number;
  totalValue: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  location: string;
}

const Inventory: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [showItemModal, setShowItemModal] = useState(false);

  const inventoryItems: InventoryItem[] = [
    {
      id: '1',
      name: 'Wireless Mouse',
      sku: 'WM-001',
      category: 'Electronics',
      currentStock: 45,
      minStock: 10,
      maxStock: 100,
      unitPrice: 25.99,
      totalValue: 1169.55,
      status: 'In Stock',
      location: 'A1-B2',
    },
    {
      id: '2',
      name: 'Office Chair',
      sku: 'OC-002',
      category: 'Furniture',
      currentStock: 5,
      minStock: 8,
      maxStock: 50,
      unitPrice: 199.99,
      totalValue: 999.95,
      status: 'Low Stock',
      location: 'B2-C3',
    },
    {
      id: '3',
      name: 'Printer Paper',
      sku: 'PP-003',
      category: 'Office Supplies',
      currentStock: 0,
      minStock: 20,
      maxStock: 200,
      unitPrice: 8.99,
      totalValue: 0,
      status: 'Out of Stock',
      location: 'C1-A1',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'In Stock': return 'green';
      case 'Low Stock': return 'orange';
      case 'Out of Stock': return 'red';
      default: return 'default';
    }
  };

  const getStockLevel = (current: number, min: number, max: number) => {
    return Math.round((current / max) * 100);
  };

  const columns: ColumnsType<InventoryItem> = [
    {
      title: 'Item',
      key: 'item',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>SKU: {record.sku}</div>
        </div>
      ),
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: 'Stock Level',
      key: 'stockLevel',
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            {record.currentStock} / {record.maxStock}
          </div>
          <Progress
            percent={getStockLevel(record.currentStock, record.minStock, record.maxStock)}
            size="small"
            status={record.status === 'Low Stock' ? 'exception' : 'normal'}
          />
        </div>
      ),
    },
    {
      title: 'Unit Price',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      render: (price) => `$${price.toFixed(2)}`,
    },
    {
      title: 'Total Value',
      dataIndex: 'totalValue',
      key: 'totalValue',
      render: (value) => `$${value.toFixed(2)}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => <Tag color={getStatusColor(status)}>{status}</Tag>,
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="text" icon={<EditOutlined />} size="small" />
          <Button type="text" icon={<SyncOutlined />} size="small" />
        </Space>
      ),
    },
  ];

  const totalItems = inventoryItems.length;
  const lowStockItems = inventoryItems.filter(item => item.status === 'Low Stock').length;
  const outOfStockItems = inventoryItems.filter(item => item.status === 'Out of Stock').length;
  const totalValue = inventoryItems.reduce((sum, item) => sum + item.totalValue, 0);

  const InventoryDashboard = () => (
    <div>
      <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Items"
              value={totalItems}
              prefix={<InboxOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Low Stock Alerts"
              value={lowStockItems}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Out of Stock"
              value={outOfStockItems}
              prefix={<MinusCircleOutlined />}
              valueStyle={{ color: '#f5222d' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Value"
              value={totalValue}
              prefix="$"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
              formatter={(value) => value?.toLocaleString()}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Card title="Low Stock Items">
            <Table
              columns={columns}
              dataSource={inventoryItems.filter(item => item.status === 'Low Stock' || item.status === 'Out of Stock')}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Recent Stock Movements">
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#666' }}>
              <SyncOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div>Stock movement tracking</div>
              <div style={{ fontSize: '12px' }}>Real-time inventory updates</div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );

  const tabItems = [
    {
      key: 'dashboard',
      label: (
        <span>
          <DashboardOutlined />
          Dashboard
        </span>
      ),
      children: <InventoryDashboard />,
    },
    {
      key: 'items',
      label: (
        <span>
          <InboxOutlined />
          {t('inventory.products')}
        </span>
      ),
      children: <ProductManagement />,
    },
    {
      key: 'movements',
      label: (
        <span>
          <SyncOutlined />
          {t('inventory.stockMovements')}
        </span>
      ),
      children: <StockMovements />,
    },
    {
      key: 'alerts',
      label: (
        <span>
          <AlertOutlined />
          Stock Alerts
        </span>
      ),
      children: (
        <Card title="Stock Level Alerts">
          <Table
            columns={columns}
            dataSource={inventoryItems.filter(item => item.status !== 'In Stock')}
            rowKey="id"
          />
        </Card>
      ),
    },
    {
      key: 'reports',
      label: (
        <span>
          <BarChartOutlined />
          Reports
        </span>
      ),
      children: (
        <Card title="Inventory Reports">
          <p>Inventory analytics and reporting system.</p>
        </Card>
      ),
    },
    {
      key: 'settings',
      label: (
        <span>
          <SettingOutlined />
          Settings
        </span>
      ),
      children: (
        <Card title="Inventory Settings">
          <p>Inventory system configuration and settings.</p>
        </Card>
      ),
    },
  ];

  return (
    <InventoryContainer>
      <div style={{ marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          {t('inventory.title')}
        </Title>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        size="large"
        type="card"
      />

      {/* Add Item Modal */}
      <Modal
        title="Add Inventory Item"
        open={showItemModal}
        onCancel={() => setShowItemModal(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="name" label="Item Name" rules={[{ required: true }]}>
                <Input placeholder="Enter item name" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="sku" label="SKU" rules={[{ required: true }]}>
                <Input placeholder="Enter SKU" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="category" label="Category" rules={[{ required: true }]}>
                <Select placeholder="Select category">
                  <Option value="Electronics">Electronics</Option>
                  <Option value="Furniture">Furniture</Option>
                  <Option value="Office Supplies">Office Supplies</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="location" label="Location">
                <Input placeholder="Enter storage location" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="currentStock" label="Current Stock" rules={[{ required: true }]}>
                <InputNumber style={{ width: '100%' }} placeholder="0" min={0} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="minStock" label="Min Stock" rules={[{ required: true }]}>
                <InputNumber style={{ width: '100%' }} placeholder="0" min={0} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="maxStock" label="Max Stock" rules={[{ required: true }]}>
                <InputNumber style={{ width: '100%' }} placeholder="0" min={0} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="unitPrice" label="Unit Price" rules={[{ required: true }]}>
            <InputNumber
              style={{ width: '100%' }}
              placeholder="0.00"
              min={0}
              step={0.01}
              formatter={(value) => `$ ${value}`}
              parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowItemModal(false)}>Cancel</Button>
              <Button type="primary">Add Item</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </InventoryContainer>
  );
};

export default Inventory;
