"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditFinding = void 0;
const typeorm_1 = require("typeorm");
const audit_report_entity_1 = require("./audit-report.entity");
const user_entity_1 = require("../../user/entities/user.entity");
let AuditFinding = class AuditFinding {
    id;
    auditReportId;
    category;
    subcategory;
    severity;
    title;
    description;
    criteria;
    condition;
    cause;
    effect;
    recommendation;
    managementResponse;
    correctiveAction;
    status;
    department;
    processArea;
    assignedTo;
    dueDate;
    resolvedDate;
    verifiedDate;
    riskRating;
    evidence;
    rootCauseAnalysis;
    remediation;
    financialImpact;
    complianceReferences;
    recurrenceCount;
    lastOccurrence;
    relatedFindings;
    followUpActions;
    auditorsNotes;
    managementNotes;
    createdBy;
    updatedBy;
    createdDate;
    updatedDate;
    auditReport;
    creator;
    updater;
    assignee;
};
exports.AuditFinding = AuditFinding;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], AuditFinding.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'audit_report_id' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "auditReportId", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "category", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "subcategory", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['Low', 'Medium', 'High', 'Critical'],
        default: 'Medium',
    }),
    __metadata("design:type", String)
], AuditFinding.prototype, "severity", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "title", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "description", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "criteria", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "condition", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "cause", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "effect", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "recommendation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditFinding.prototype, "managementResponse", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditFinding.prototype, "correctiveAction", void 0);
__decorate([
    (0, typeorm_1.Column)({
        type: 'enum',
        enum: ['Open', 'In Progress', 'Resolved', 'Closed'],
        default: 'Open',
    }),
    __metadata("design:type", String)
], AuditFinding.prototype, "status", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "department", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "processArea", void 0);
__decorate([
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], AuditFinding.prototype, "assignedTo", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date' }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "dueDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "resolvedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "verifiedDate", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditFinding.prototype, "riskRating", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditFinding.prototype, "evidence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditFinding.prototype, "rootCauseAnalysis", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Object)
], AuditFinding.prototype, "remediation", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'decimal', precision: 15, scale: 2, nullable: true }),
    __metadata("design:type", Number)
], AuditFinding.prototype, "financialImpact", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditFinding.prototype, "complianceReferences", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'int', default: 0 }),
    __metadata("design:type", Number)
], AuditFinding.prototype, "recurrenceCount", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'date', nullable: true }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "lastOccurrence", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditFinding.prototype, "relatedFindings", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json', nullable: true }),
    __metadata("design:type", Array)
], AuditFinding.prototype, "followUpActions", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditFinding.prototype, "auditorsNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'text', nullable: true }),
    __metadata("design:type", String)
], AuditFinding.prototype, "managementNotes", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'created_by' }),
    __metadata("design:type", String)
], AuditFinding.prototype, "createdBy", void 0);
__decorate([
    (0, typeorm_1.Column)({ name: 'updated_by', nullable: true }),
    __metadata("design:type", String)
], AuditFinding.prototype, "updatedBy", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)({ name: 'created_date' }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "createdDate", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)({ name: 'updated_date' }),
    __metadata("design:type", Date)
], AuditFinding.prototype, "updatedDate", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => audit_report_entity_1.AuditReport, (report) => report.findings),
    (0, typeorm_1.JoinColumn)({ name: 'audit_report_id' }),
    __metadata("design:type", audit_report_entity_1.AuditReport)
], AuditFinding.prototype, "auditReport", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], AuditFinding.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], AuditFinding.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'assigned_to' }),
    __metadata("design:type", user_entity_1.User)
], AuditFinding.prototype, "assignee", void 0);
exports.AuditFinding = AuditFinding = __decorate([
    (0, typeorm_1.Entity)('audit_findings')
], AuditFinding);
//# sourceMappingURL=audit-finding.entity.js.map