import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Typography,
  DatePicker,
  Statistic,
  Progress,
  Tooltip,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  UserOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface LeaveRequest {
  id: string;
  employeeId: string;
  employeeName: string;
  department: string;
  leaveType: 'annual' | 'sick' | 'personal' | 'maternity' | 'emergency' | 'unpaid';
  startDate: string;
  endDate: string;
  days: number;
  reason: string;
  status: 'pending' | 'approved' | 'rejected' | 'cancelled';
  appliedDate: string;
  approvedBy?: string;
  approvedDate?: string;
  comments?: string;
}

interface LeaveBalance {
  employeeId: string;
  employeeName: string;
  department: string;
  annual: { total: number; used: number; remaining: number };
  sick: { total: number; used: number; remaining: number };
  personal: { total: number; used: number; remaining: number };
}

const LeaveManagement: React.FC = () => {
  const [leaveRequests, setLeaveRequests] = useState<LeaveRequest[]>([
    {
      id: '1',
      employeeId: 'EMP001',
      employeeName: 'John Smith',
      department: 'IT',
      leaveType: 'annual',
      startDate: '2025-02-01',
      endDate: '2025-02-05',
      days: 5,
      reason: 'Family vacation',
      status: 'pending',
      appliedDate: '2025-01-20',
    },
    {
      id: '2',
      employeeId: 'EMP002',
      employeeName: 'Sarah Johnson',
      department: 'Finance',
      leaveType: 'sick',
      startDate: '2025-01-22',
      endDate: '2025-01-22',
      days: 1,
      reason: 'Medical appointment',
      status: 'approved',
      appliedDate: '2025-01-21',
      approvedBy: 'HR Manager',
      approvedDate: '2025-01-21',
    },
  ]);

  const [leaveBalances] = useState<LeaveBalance[]>([
    {
      employeeId: 'EMP001',
      employeeName: 'John Smith',
      department: 'IT',
      annual: { total: 25, used: 8, remaining: 17 },
      sick: { total: 10, used: 2, remaining: 8 },
      personal: { total: 5, used: 1, remaining: 4 },
    },
    {
      employeeId: 'EMP002',
      employeeName: 'Sarah Johnson',
      department: 'Finance',
      annual: { total: 25, used: 12, remaining: 13 },
      sick: { total: 10, used: 3, remaining: 7 },
      personal: { total: 5, used: 0, remaining: 5 },
    },
  ]);

  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState<LeaveRequest | null>(null);
  const [searchText, setSearchText] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [leaveTypeFilter, setLeaveTypeFilter] = useState('all');
  const [form] = Form.useForm();
  const [approvalForm] = Form.useForm();

  const departments = ['IT', 'Finance', 'Sales', 'HR', 'Marketing', 'Operations'];
  const leaveTypes = [
    { value: 'annual', label: 'Annual Leave', color: 'blue' },
    { value: 'sick', label: 'Sick Leave', color: 'red' },
    { value: 'personal', label: 'Personal Leave', color: 'green' },
    { value: 'maternity', label: 'Maternity Leave', color: 'purple' },
    { value: 'emergency', label: 'Emergency Leave', color: 'orange' },
    { value: 'unpaid', label: 'Unpaid Leave', color: 'gray' },
  ];

  const getStatusColor = (status: string) => {
    const colors = {
      'pending': 'orange',
      'approved': 'green',
      'rejected': 'red',
      'cancelled': 'gray',
    };
    return colors[status as keyof typeof colors] || 'default';
  };

  const getLeaveTypeColor = (type: string) => {
    const typeConfig = leaveTypes.find(t => t.value === type);
    return typeConfig?.color || 'default';
  };

  const filteredRequests = leaveRequests.filter(request => {
    const matchesSearch = request.employeeName.toLowerCase().includes(searchText.toLowerCase()) ||
                         request.employeeId.toLowerCase().includes(searchText.toLowerCase());
    const matchesDepartment = departmentFilter === 'all' || request.department === departmentFilter;
    const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
    const matchesLeaveType = leaveTypeFilter === 'all' || request.leaveType === leaveTypeFilter;
    return matchesSearch && matchesDepartment && matchesStatus && matchesLeaveType;
  });

  const handleSubmitLeave = async (values: any) => {
    try {
      const startDate = values.dateRange[0];
      const endDate = values.dateRange[1];
      const days = endDate.diff(startDate, 'day') + 1;

      const newRequest: LeaveRequest = {
        id: (leaveRequests.length + 1).toString(),
        employeeId: values.employeeId,
        employeeName: 'Current User', // In real app, get from auth context
        department: 'IT', // In real app, get from employee data
        leaveType: values.leaveType,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
        days,
        reason: values.reason,
        status: 'pending',
        appliedDate: dayjs().format('YYYY-MM-DD'),
      };

      setLeaveRequests([newRequest, ...leaveRequests]);
      setShowLeaveModal(false);
      form.resetFields();
      message.success('Leave request submitted successfully!');
    } catch (error) {
      message.error('Failed to submit leave request');
    }
  };

  const handleApproveReject = async (values: any) => {
    try {
      if (!selectedRequest) return;

      setLeaveRequests(prev => prev.map(request => 
        request.id === selectedRequest.id 
          ? {
              ...request,
              status: values.decision,
              approvedBy: 'Current Manager',
              approvedDate: dayjs().format('YYYY-MM-DD'),
              comments: values.comments,
            }
          : request
      ));

      setShowApprovalModal(false);
      setSelectedRequest(null);
      approvalForm.resetFields();
      message.success(`Leave request ${values.decision} successfully!`);
    } catch (error) {
      message.error('Failed to process leave request');
    }
  };

  const requestColumns = [
    {
      title: 'Employee',
      key: 'employee',
      width: 200,
      render: (_, record: LeaveRequest) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.employeeName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.employeeId} • {record.department}
          </div>
        </div>
      ),
    },
    {
      title: 'Leave Type',
      dataIndex: 'leaveType',
      key: 'leaveType',
      width: 120,
      render: (type: string) => {
        const typeConfig = leaveTypes.find(t => t.value === type);
        return (
          <Tag color={getLeaveTypeColor(type)}>
            {typeConfig?.label || type.toUpperCase()}
          </Tag>
        );
      },
    },
    {
      title: 'Duration',
      key: 'duration',
      width: 150,
      render: (_, record: LeaveRequest) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.days} day(s)</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(record.startDate).format('MMM DD')} - {dayjs(record.endDate).format('MMM DD')}
          </div>
        </div>
      ),
    },
    {
      title: 'Reason',
      dataIndex: 'reason',
      key: 'reason',
      ellipsis: true,
    },
    {
      title: 'Applied Date',
      dataIndex: 'appliedDate',
      key: 'appliedDate',
      width: 120,
      render: (date: string) => dayjs(date).format('MMM DD, YYYY'),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      render: (_, record: LeaveRequest) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button type="text" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          {record.status === 'pending' && (
            <>
              <Tooltip title="Approve/Reject">
                <Button 
                  type="text" 
                  icon={<CheckCircleOutlined />} 
                  size="small"
                  onClick={() => {
                    setSelectedRequest(record);
                    setShowApprovalModal(true);
                  }}
                />
              </Tooltip>
              <Tooltip title="Edit">
                <Button type="text" icon={<EditOutlined />} size="small" />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  const balanceColumns = [
    {
      title: 'Employee',
      key: 'employee',
      render: (_, record: LeaveBalance) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.employeeName}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.employeeId} • {record.department}
          </div>
        </div>
      ),
    },
    {
      title: 'Annual Leave',
      key: 'annual',
      render: (_, record: LeaveBalance) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Text strong>{record.annual.remaining}</Text> / {record.annual.total} days
          </div>
          <Progress 
            percent={(record.annual.remaining / record.annual.total) * 100}
            size="small"
            strokeColor="#52c41a"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: 'Sick Leave',
      key: 'sick',
      render: (_, record: LeaveBalance) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Text strong>{record.sick.remaining}</Text> / {record.sick.total} days
          </div>
          <Progress 
            percent={(record.sick.remaining / record.sick.total) * 100}
            size="small"
            strokeColor="#fa8c16"
            showInfo={false}
          />
        </div>
      ),
    },
    {
      title: 'Personal Leave',
      key: 'personal',
      render: (_, record: LeaveBalance) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Text strong>{record.personal.remaining}</Text> / {record.personal.total} days
          </div>
          <Progress 
            percent={(record.personal.remaining / record.personal.total) * 100}
            size="small"
            strokeColor="#1890ff"
            showInfo={false}
          />
        </div>
      ),
    },
  ];

  // Calculate statistics
  const pendingRequests = leaveRequests.filter(r => r.status === 'pending').length;
  const approvedRequests = leaveRequests.filter(r => r.status === 'approved').length;
  const totalDaysRequested = leaveRequests.reduce((sum, r) => sum + r.days, 0);
  const avgLeaveDays = leaveRequests.length > 0 ? totalDaysRequested / leaveRequests.length : 0;

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Statistics */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Pending Requests"
                  value={pendingRequests}
                  prefix={<ClockCircleOutlined />}
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Approved Requests"
                  value={approvedRequests}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Leave Days"
                  value={totalDaysRequested}
                  prefix={<CalendarOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Avg Leave Days"
                  value={avgLeaveDays.toFixed(1)}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Leave Requests */}
        <Col xs={24} lg={16}>
          <Card
            title="Leave Requests"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowLeaveModal(true)}
              >
                Apply for Leave
              </Button>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={6}>
                <Input
                  placeholder="Search employees..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Department"
                  style={{ width: '100%' }}
                  value={departmentFilter}
                  onChange={setDepartmentFilter}
                >
                  <Select.Option value="all">All Departments</Select.Option>
                  {departments.map(dept => (
                    <Select.Option key={dept} value={dept}>{dept}</Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Status"
                  style={{ width: '100%' }}
                  value={statusFilter}
                  onChange={setStatusFilter}
                >
                  <Select.Option value="all">All Statuses</Select.Option>
                  <Select.Option value="pending">Pending</Select.Option>
                  <Select.Option value="approved">Approved</Select.Option>
                  <Select.Option value="rejected">Rejected</Select.Option>
                </Select>
              </Col>
              <Col xs={24} sm={6}>
                <Select
                  placeholder="Leave Type"
                  style={{ width: '100%' }}
                  value={leaveTypeFilter}
                  onChange={setLeaveTypeFilter}
                >
                  <Select.Option value="all">All Types</Select.Option>
                  {leaveTypes.map(type => (
                    <Select.Option key={type.value} value={type.value}>
                      {type.label}
                    </Select.Option>
                  ))}
                </Select>
              </Col>
            </Row>

            <Table
              columns={requestColumns}
              dataSource={filteredRequests}
              rowKey="id"
              pagination={{
                pageSize: 8,
                showSizeChanger: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} requests`,
              }}
              scroll={{ x: 1000 }}
            />
          </Card>
        </Col>

        {/* Leave Balances */}
        <Col xs={24} lg={8}>
          <Card title="Leave Balances">
            <Table
              columns={balanceColumns}
              dataSource={leaveBalances}
              rowKey="employeeId"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      {/* Apply Leave Modal */}
      <Modal
        title="Apply for Leave"
        open={showLeaveModal}
        onCancel={() => {
          setShowLeaveModal(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmitLeave}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="employeeId"
                label="Employee"
                rules={[{ required: true, message: 'Please select employee' }]}
              >
                <Select placeholder="Select employee">
                  <Select.Option value="EMP001">John Smith (EMP001)</Select.Option>
                  <Select.Option value="EMP002">Sarah Johnson (EMP002)</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="leaveType"
                label="Leave Type"
                rules={[{ required: true, message: 'Please select leave type' }]}
              >
                <Select placeholder="Select leave type">
                  {leaveTypes.map(type => (
                    <Select.Option key={type.value} value={type.value}>
                      {type.label}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="dateRange"
                label="Leave Period"
                rules={[{ required: true, message: 'Please select leave period' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                name="reason"
                label="Reason"
                rules={[{ required: true, message: 'Please enter reason' }]}
              >
                <Input.TextArea rows={3} placeholder="Enter reason for leave" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 16 }}>
            <Space>
              <Button onClick={() => setShowLeaveModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Submit Request</Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* Approval Modal */}
      <Modal
        title={`Review Leave Request - ${selectedRequest?.employeeName}`}
        open={showApprovalModal}
        onCancel={() => {
          setShowApprovalModal(false);
          setSelectedRequest(null);
          approvalForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {selectedRequest && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12}>
                  <Text strong>Employee: </Text>
                  <Text>{selectedRequest.employeeName}</Text>
                </Col>
                <Col xs={24} sm={12}>
                  <Text strong>Leave Type: </Text>
                  <Tag color={getLeaveTypeColor(selectedRequest.leaveType)}>
                    {leaveTypes.find(t => t.value === selectedRequest.leaveType)?.label}
                  </Tag>
                </Col>
                <Col xs={24} sm={12}>
                  <Text strong>Duration: </Text>
                  <Text>{selectedRequest.days} day(s)</Text>
                </Col>
                <Col xs={24} sm={12}>
                  <Text strong>Period: </Text>
                  <Text>
                    {dayjs(selectedRequest.startDate).format('MMM DD')} - {dayjs(selectedRequest.endDate).format('MMM DD, YYYY')}
                  </Text>
                </Col>
                <Col xs={24}>
                  <Text strong>Reason: </Text>
                  <Text>{selectedRequest.reason}</Text>
                </Col>
              </Row>
            </Card>

            <Form form={approvalForm} layout="vertical" onFinish={handleApproveReject}>
              <Form.Item
                name="decision"
                label="Decision"
                rules={[{ required: true, message: 'Please make a decision' }]}
              >
                <Select placeholder="Select decision">
                  <Select.Option value="approved">Approve</Select.Option>
                  <Select.Option value="rejected">Reject</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item name="comments" label="Comments">
                <Input.TextArea rows={3} placeholder="Enter comments (optional)" />
              </Form.Item>

              <div style={{ textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setShowApprovalModal(false)}>Cancel</Button>
                  <Button type="primary" htmlType="submit">Submit Decision</Button>
                </Space>
              </div>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LeaveManagement;
