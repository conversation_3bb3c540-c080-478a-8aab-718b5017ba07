import React, { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  Row,
  Col,
  message,
  Tag,
  Progress,
  Typography,
  Divider,
  Statistic,
  DatePicker,
  InputNumber,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  Bar<PERSON><PERSON>Outlined,
  DollarOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface BudgetItem {
  id: string;
  category: string;
  department: string;
  budgetAmount: number;
  actualAmount: number;
  variance: number;
  variancePercent: number;
  period: string;
  status: 'under' | 'over' | 'on-track';
}

interface BudgetCategory {
  id: string;
  name: string;
  type: 'revenue' | 'expense';
  totalBudget: number;
  totalActual: number;
  items: BudgetItem[];
}

const BudgetManagement: React.FC = () => {
  const [budgetItems, setBudgetItems] = useState<BudgetItem[]>([
    {
      id: '1',
      category: 'Marketing',
      department: 'Marketing',
      budgetAmount: 50000,
      actualAmount: 45000,
      variance: 5000,
      variancePercent: 10,
      period: '2025-Q1',
      status: 'under',
    },
    {
      id: '2',
      category: 'Office Supplies',
      department: 'Administration',
      budgetAmount: 15000,
      actualAmount: 18000,
      variance: -3000,
      variancePercent: -20,
      period: '2025-Q1',
      status: 'over',
    },
    {
      id: '3',
      category: 'Software Licenses',
      department: 'IT',
      budgetAmount: 25000,
      actualAmount: 25000,
      variance: 0,
      variancePercent: 0,
      period: '2025-Q1',
      status: 'on-track',
    },
  ]);

  const [showBudgetModal, setShowBudgetModal] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedPeriod, setSelectedPeriod] = useState('2025-Q1');
  const [form] = Form.useForm();

  const departments = ['Marketing', 'Administration', 'IT', 'Sales', 'Finance', 'HR'];
  const periods = ['2025-Q1', '2025-Q2', '2025-Q3', '2025-Q4'];

  const filteredItems = budgetItems.filter(item => {
    const matchesSearch = item.category.toLowerCase().includes(searchText.toLowerCase()) ||
                         item.department.toLowerCase().includes(searchText.toLowerCase());
    const matchesDepartment = selectedDepartment === 'all' || item.department === selectedDepartment;
    const matchesPeriod = item.period === selectedPeriod;
    return matchesSearch && matchesDepartment && matchesPeriod;
  });

  const totalBudget = filteredItems.reduce((sum, item) => sum + item.budgetAmount, 0);
  const totalActual = filteredItems.reduce((sum, item) => sum + item.actualAmount, 0);
  const totalVariance = totalBudget - totalActual;
  const totalVariancePercent = totalBudget > 0 ? (totalVariance / totalBudget) * 100 : 0;

  const handleCreateBudget = async (values: any) => {
    try {
      const budgetAmount = values.budgetAmount;
      const actualAmount = values.actualAmount || 0;
      const variance = budgetAmount - actualAmount;
      const variancePercent = budgetAmount > 0 ? (variance / budgetAmount) * 100 : 0;
      
      let status: 'under' | 'over' | 'on-track' = 'on-track';
      if (variancePercent > 5) status = 'under';
      else if (variancePercent < -5) status = 'over';

      const newBudgetItem: BudgetItem = {
        id: (budgetItems.length + 1).toString(),
        category: values.category,
        department: values.department,
        budgetAmount,
        actualAmount,
        variance,
        variancePercent,
        period: values.period,
        status,
      };

      setBudgetItems([newBudgetItem, ...budgetItems]);
      setShowBudgetModal(false);
      form.resetFields();
      message.success('Budget item created successfully!');
    } catch (error) {
      message.error('Failed to create budget item');
    }
  };

  const columns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 150,
    },
    {
      title: 'Department',
      dataIndex: 'department',
      key: 'department',
      width: 120,
      render: (dept: string) => <Tag color="blue">{dept}</Tag>,
    },
    {
      title: 'Budget Amount',
      dataIndex: 'budgetAmount',
      key: 'budgetAmount',
      width: 130,
      render: (amount: number) => `$${amount.toLocaleString()}`,
      align: 'right' as const,
    },
    {
      title: 'Actual Amount',
      dataIndex: 'actualAmount',
      key: 'actualAmount',
      width: 130,
      render: (amount: number) => `$${amount.toLocaleString()}`,
      align: 'right' as const,
    },
    {
      title: 'Variance',
      dataIndex: 'variance',
      key: 'variance',
      width: 120,
      render: (variance: number) => (
        <span style={{ color: variance >= 0 ? '#52c41a' : '#f5222d' }}>
          {variance >= 0 ? '+' : ''}${variance.toLocaleString()}
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Variance %',
      dataIndex: 'variancePercent',
      key: 'variancePercent',
      width: 120,
      render: (percent: number) => (
        <span style={{ color: percent >= 0 ? '#52c41a' : '#f5222d' }}>
          {percent >= 0 ? '+' : ''}{percent.toFixed(1)}%
        </span>
      ),
      align: 'right' as const,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const colors = {
          'under': 'green',
          'over': 'red',
          'on-track': 'blue',
        };
        const labels = {
          'under': 'Under Budget',
          'over': 'Over Budget',
          'on-track': 'On Track',
        };
        return <Tag color={colors[status as keyof typeof colors]}>{labels[status as keyof typeof labels]}</Tag>;
      },
    },
    {
      title: 'Progress',
      key: 'progress',
      width: 150,
      render: (_, record: BudgetItem) => {
        const percent = record.budgetAmount > 0 ? (record.actualAmount / record.budgetAmount) * 100 : 0;
        const strokeColor = percent > 100 ? '#f5222d' : percent > 90 ? '#faad14' : '#52c41a';
        return (
          <Progress
            percent={Math.min(percent, 100)}
            size="small"
            strokeColor={strokeColor}
            format={() => `${percent.toFixed(0)}%`}
          />
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (_, record: BudgetItem) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} size="small" />
          <Button type="text" icon={<EditOutlined />} size="small" />
          <Button type="text" icon={<DeleteOutlined />} size="small" danger />
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Row gutter={[24, 24]}>
        {/* Summary Cards */}
        <Col xs={24}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Budget"
                  value={totalBudget}
                  prefix="$"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Total Actual"
                  value={totalActual}
                  prefix="$"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Variance"
                  value={totalVariance}
                  prefix="$"
                  valueStyle={{ color: totalVariance >= 0 ? '#52c41a' : '#f5222d' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={6}>
              <Card>
                <Statistic
                  title="Variance %"
                  value={totalVariancePercent.toFixed(1)}
                  suffix="%"
                  valueStyle={{ color: totalVariancePercent >= 0 ? '#52c41a' : '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>
        </Col>

        {/* Budget Management */}
        <Col xs={24}>
          <Card
            title={`Budget Management - ${selectedPeriod}`}
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setShowBudgetModal(true)}
                >
                  Add Budget Item
                </Button>
              </Space>
            }
          >
            <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
              <Col xs={24} sm={8}>
                <Input
                  placeholder="Search categories..."
                  prefix={<SearchOutlined />}
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Filter by department"
                  style={{ width: '100%' }}
                  value={selectedDepartment}
                  onChange={setSelectedDepartment}
                >
                  <Select.Option value="all">All Departments</Select.Option>
                  {departments.map(dept => (
                    <Select.Option key={dept} value={dept}>{dept}</Select.Option>
                  ))}
                </Select>
              </Col>
              <Col xs={24} sm={8}>
                <Select
                  placeholder="Select period"
                  style={{ width: '100%' }}
                  value={selectedPeriod}
                  onChange={setSelectedPeriod}
                >
                  {periods.map(period => (
                    <Select.Option key={period} value={period}>{period}</Select.Option>
                  ))}
                </Select>
              </Col>
            </Row>

            <Table
              columns={columns}
              dataSource={filteredItems}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} budget items`,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </Col>
      </Row>

      {/* Create Budget Modal */}
      <Modal
        title="Add Budget Item"
        open={showBudgetModal}
        onCancel={() => setShowBudgetModal(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleCreateBudget}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                name="category"
                label="Category"
                rules={[{ required: true, message: 'Please enter category' }]}
              >
                <Input placeholder="e.g., Marketing, Office Supplies" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="department"
                label="Department"
                rules={[{ required: true, message: 'Please select department' }]}
              >
                <Select placeholder="Select department">
                  {departments.map(dept => (
                    <Select.Option key={dept} value={dept}>{dept}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="period"
                label="Budget Period"
                rules={[{ required: true, message: 'Please select period' }]}
              >
                <Select placeholder="Select period">
                  {periods.map(period => (
                    <Select.Option key={period} value={period}>{period}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name="budgetAmount"
                label="Budget Amount"
                rules={[{ required: true, message: 'Please enter budget amount' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="0.00"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item name="actualAmount" label="Actual Amount (Optional)">
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="0.00"
                  formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, ''))}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setShowBudgetModal(false)}>Cancel</Button>
              <Button type="primary" htmlType="submit">Add Budget Item</Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default BudgetManagement;
